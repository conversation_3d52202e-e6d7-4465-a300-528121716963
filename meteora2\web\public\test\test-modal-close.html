<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔒 模态框关闭功能测试</title>

    <!-- Bootstrap 5 -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <style>
        body {
            background-color: #1a1b2e;
            color: #ffffff;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
            padding: 2rem;
        }

        .test-card {
            background: #16213e;
            border: 1px solid #0f3460;
            border-radius: 12px;
            padding: 2rem;
            margin-bottom: 2rem;
        }

        .log-area {
            background: #000;
            color: #00ff00;
            font-family: 'Courier New', monospace;
            padding: 1rem;
            border-radius: 8px;
            max-height: 300px;
            overflow-y: auto;
            font-size: 0.9rem;
        }

        .btn-test {
            margin: 0.5rem;
            min-width: 120px;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-ok {
            background-color: #28a745;
        }

        .status-error {
            background-color: #dc3545;
        }

        .status-warning {
            background-color: #ffc107;
        }
    </style>
</head>

<body>

    <div class="container">
        <h1 class="text-center mb-4">🔒 模态框关闭功能测试</h1>

        <!-- 控制面板 -->
        <div class="test-card">
            <h3 class="mb-3">🎮 测试控制</h3>
            <div class="row">
                <div class="col-md-6">
                    <button class="btn btn-primary btn-test" onclick="initTest()">
                        🚀 初始化测试
                    </button>
                    <button class="btn btn-success btn-test" onclick="showModal()" disabled id="show-btn">
                        📝 显示模态框
                    </button>
                    <button class="btn btn-warning btn-test" onclick="testCloseButtons()">
                        🔒 测试关闭按钮
                    </button>
                </div>
                <div class="col-md-6">
                    <button class="btn btn-info btn-test" onclick="testEscKey()">
                        ⌨️ 测试ESC键
                    </button>
                    <button class="btn btn-secondary btn-test" onclick="testBackdropClick()">
                        🖱️ 测试背景点击
                    </button>
                    <button class="btn btn-danger btn-test" onclick="clearLog()">
                        🗑️ 清空日志
                    </button>
                </div>
            </div>
        </div>

        <!-- 状态显示 -->
        <div class="test-card">
            <h3 class="mb-3">📊 当前状态</h3>
            <div class="row">
                <div class="col-md-4">
                    <p><span class="status-indicator status-error" id="modal-status"></span>模态框状态: <span
                            id="modal-text">未初始化</span></p>
                </div>
                <div class="col-md-4">
                    <p><span class="status-indicator status-error" id="body-status"></span>Body状态: <span
                            id="body-text">正常</span></p>
                </div>
                <div class="col-md-4">
                    <p><span class="status-indicator status-error" id="functions-status"></span>关闭函数: <span
                            id="functions-text">未绑定</span></p>
                </div>
            </div>
        </div>

        <!-- 实时日志 -->
        <div class="test-card">
            <h3 class="mb-3">📜 实时日志</h3>
            <div class="log-area" id="log-area">
                等待测试开始...
            </div>
        </div>

        <!-- 策略管理器容器 -->
        <div id="strategy-test-container" style="display: none;">
            <!-- 策略管理器将在这里显示 -->
        </div>
    </div>

    <!-- 引入修复后的策略模块 -->
    <script src="../public/js/components/strategy/strategy-forms-simple.js"></script>
    <script src="../public/js/components/strategy/simple-y-manager.js"></script>

    <script>
        let testManager = null;
        let logMessages = [];
        let modalCheckInterval = null;

        // 日志函数
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const prefix = {
                'info': '[INFO]',
                'success': '[SUCCESS]',
                'error': '[ERROR]',
                'warning': '[WARNING]'
            }[type] || '[INFO]';

            const logMessage = `${timestamp} ${prefix} ${message}`;
            logMessages.push(logMessage);

            const logArea = document.getElementById('log-area');
            logArea.innerHTML = logMessages.slice(-50).join('<br>');
            logArea.scrollTop = logArea.scrollHeight;
        }

        function clearLog() {
            logMessages = [];
            document.getElementById('log-area').innerHTML = '日志已清空...';
        }

        // 状态更新函数
        function updateStatus() {
            const modal = document.getElementById('create-simple-y-modal');
            const modalStatus = document.getElementById('modal-status');
            const modalText = document.getElementById('modal-text');
            const bodyStatus = document.getElementById('body-status');
            const bodyText = document.getElementById('body-text');
            const functionsStatus = document.getElementById('functions-status');
            const functionsText = document.getElementById('functions-text');

            // 检查模态框状态
            if (modal) {
                const isVisible = modal.style.display === 'flex';
                modalStatus.className = `status-indicator ${isVisible ? 'status-ok' : 'status-error'}`;
                modalText.textContent = isVisible ? '显示中' : '已隐藏';
            } else {
                modalStatus.className = 'status-indicator status-warning';
                modalText.textContent = '不存在';
            }

            // 检查body状态
            const hasModalOpen = document.body.classList.contains('modal-open');
            const hasOverflowHidden = document.body.style.overflow === 'hidden';
            bodyStatus.className = `status-indicator ${hasModalOpen || hasOverflowHidden ? 'status-warning' : 'status-ok'}`;
            bodyText.textContent = hasModalOpen ? '模态框模式' : '正常';

            // 检查关闭函数
            const hasCloseFunction = typeof window.closeSimpleYModal === 'function';
            functionsStatus.className = `status-indicator ${hasCloseFunction ? 'status-ok' : 'status-error'}`;
            functionsText.textContent = hasCloseFunction ? '已绑定' : '未绑定';
        }

        // 测试函数
        async function initTest() {
            try {
                log('开始初始化测试环境...', 'info');

                // 检查类是否加载
                if (!window.SimpleYStrategyManager || !window.SimpleYStrategyForms) {
                    throw new Error('策略模块未正确加载');
                }
                log('策略模块加载成功', 'success');

                // 创建管理器实例
                testManager = new SimpleYStrategyManager('strategy-test-container');
                const success = await testManager.init();

                if (success) {
                    log('策略管理器初始化成功', 'success');
                    document.getElementById('show-btn').disabled = false;

                    // 开始状态监控
                    modalCheckInterval = setInterval(updateStatus, 500);
                    updateStatus();
                } else {
                    throw new Error('策略管理器初始化失败');
                }

            } catch (error) {
                log('初始化失败: ' + error.message, 'error');
            }
        }

        function showModal() {
            try {
                log('尝试显示模态框...', 'info');
                if (testManager && testManager.forms) {
                    testManager.showCreateStrategyForm();
                    log('模态框显示命令已发送', 'success');
                } else {
                    throw new Error('管理器未初始化');
                }
            } catch (error) {
                log('显示模态框失败: ' + error.message, 'error');
            }
        }

        function testCloseButtons() {
            const modal = document.getElementById('create-simple-y-modal');
            if (!modal || modal.style.display !== 'flex') {
                log('请先显示模态框', 'warning');
                return;
            }

            log('测试关闭按钮...', 'info');

            // 检查关闭按钮
            const closeBtns = modal.querySelectorAll('button[onclick*="closeSimpleYModal"]');
            log(`找到 ${closeBtns.length} 个关闭按钮`, 'info');

            if (closeBtns.length > 0) {
                log('模拟点击第一个关闭按钮...', 'info');
                setTimeout(() => {
                    closeBtns[0].click();
                    log('关闭按钮点击完成', 'success');
                }, 1000);
            } else {
                log('未找到关闭按钮', 'error');
            }
        }

        function testEscKey() {
            const modal = document.getElementById('create-simple-y-modal');
            if (!modal || modal.style.display !== 'flex') {
                log('请先显示模态框', 'warning');
                return;
            }

            log('测试ESC键关闭...', 'info');
            setTimeout(() => {
                const escEvent = new KeyboardEvent('keydown', { key: 'Escape' });
                document.dispatchEvent(escEvent);
                log('ESC键事件已触发', 'success');
            }, 1000);
        }

        function testBackdropClick() {
            const modal = document.getElementById('create-simple-y-modal');
            if (!modal || modal.style.display !== 'flex') {
                log('请先显示模态框', 'warning');
                return;
            }

            log('测试背景点击关闭...', 'info');
            setTimeout(() => {
                modal.click();
                log('背景点击事件已触发', 'success');
            }, 1000);
        }

        // 全局错误处理
        window.addEventListener('error', function (e) {
            log('页面错误: ' + e.message, 'error');
        });

        // 初始化状态显示
        document.addEventListener('DOMContentLoaded', function () {
            log('测试页面加载完成', 'success');
            updateStatus();
        });
    </script>
</body>

</html>