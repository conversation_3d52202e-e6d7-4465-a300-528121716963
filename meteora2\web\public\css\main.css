/* 🎨 DLMM流动性管理系统 - 主样式文件 */
/* 基于现代化仪表盘设计的CSS变量系统 */

/* ✅ CSS变量系统 - 参考原始设计 */
:root {
    /* === 主要颜色 === */
    --primary-color: #3b82f6;
    /* 主色调 - 蓝色 */
    --primary-color-rgb: 59, 130, 246;
    /* 主色调RGB值 */
    --primary-hover: #2563eb;
    /* 主色调悬停 */
    --primary-light: #dbeafe;
    /* 主色调浅色 */
    --primary-dark: #1d4ed8;
    /* 主色调深色 */
    --secondary-color: #6b7280;
    /* 次要颜色 - 灰色 */
    --accent-color: #10b981;
    /* 强调色 - 绿色 */

    /* === 背景色系 === */
    --bg-primary: #ffffff;
    /* 主背景 */
    --bg-secondary: #f8fafc;
    /* 次要背景 */
    --bg-tertiary: #f1f5f9;
    /* 第三背景 */
    --bg-card: #ffffff;
    /* 卡片背景 */
    --bg-overlay: rgba(0, 0, 0, 0.5);
    /* 遮罩背景 */

    /* === 文字色系 === */
    --text-primary: #1f2937;
    /* 主文字 */
    --text-secondary: #6b7280;
    /* 次要文字 */
    --text-muted: #9ca3af;
    /* 辅助文字 */
    --text-inverse: #ffffff;
    /* 反色文字 */

    /* === 边框和阴影 === */
    --border-color: #e5e7eb;
    --border-color-light: #f3f4f6;
    --border-color-dark: #d1d5db;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);

    /* === 状态颜色 === */
    --success-color: #10b981;
    /* 成功状态 */
    --success-color-rgb: 16, 185, 129;
    /* 成功状态RGB */
    --success-light: #d1fae5;
    /* 成功浅色 */
    --warning-color: #f59e0b;
    /* 警告状态 */
    --warning-color-rgb: 245, 158, 11;
    /* 警告状态RGB */
    --warning-light: #fef3c7;
    /* 警告浅色 */
    --error-color: #ef4444;
    /* 错误状态 */
    --error-color-rgb: 239, 68, 68;
    /* 错误状态RGB */
    --error-light: #fee2e2;
    /* 错误浅色 */
    --info-color: #3b82f6;
    /* 信息状态 */
    --info-light: #dbeafe;
    /* 信息浅色 */

    /* === 布局尺寸 === */
    --header-height: 70px;
    --sidebar-width: 250px;
    --sidebar-width-collapsed: 60px;
    --container-max-width: 1400px;
    --content-padding: 24px;
    --card-padding: 20px;

    /* === 边框圆角 === */
    --border-radius: 8px;
    --border-radius-sm: 4px;
    --border-radius-lg: 12px;
    --border-radius-xl: 16px;
    --border-radius-full: 9999px;

    /* === 动画时间 === */
    --transition-fast: 0.15s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;

    /* === 字体大小 === */
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;

    /* === 间距系统 === */
    --spacing-1: 0.25rem;
    --spacing-2: 0.5rem;
    --spacing-3: 0.75rem;
    --spacing-4: 1rem;
    --spacing-5: 1.25rem;
    --spacing-6: 1.5rem;
    --spacing-8: 2rem;
    --spacing-10: 2.5rem;
    --spacing-12: 3rem;
    --spacing-16: 4rem;
    --spacing-20: 5rem;

    /* === Z-index层级 === */
    --z-dropdown: 1000;
    --z-sticky: 1010;
    --z-fixed: 1020;
    --z-modal-backdrop: 1030;
    --z-modal: 1040;
    --z-popover: 1050;
    --z-tooltip: 1060;
}

/* === 基础重置和设置 === */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
        'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
        sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--bg-secondary);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    overflow-x: hidden;
}

/* === 应用容器布局 === */
.app-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* === 顶部导航栏 === */
.header {
    height: var(--header-height);
    background: var(--bg-card);
    border-bottom: 1px solid var(--border-color);
    box-shadow: var(--shadow-sm);
    position: sticky;
    top: 0;
    z-index: var(--z-sticky);
}

.header-content {
    height: 100%;
    width: 100%;
    margin: 0;
    padding: 0 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.header-left {
    display: flex;
    align-items: center;
    gap: var(--spacing-6);
}

.app-title {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    margin: 0;
}

.app-icon {
    font-size: var(--font-size-2xl);
}

.connection-status {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    padding: var(--spacing-2) var(--spacing-3);
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
    font-size: var(--font-size-sm);
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: var(--border-radius-full);
    background: var(--error-color);
    transition: background-color var(--transition-fast);
}

.status-dot.connected {
    background: var(--success-color);
}

.status-dot.connecting {
    background: var(--warning-color);
    animation: pulse 1.5s infinite;
}

.status-dot.disconnected {
    background: var(--text-muted);
}

.status-dot.error {
    background: var(--error-color);
    animation: pulse 1s infinite;
}

.header-right {
    display: flex;
    align-items: center;
}

.header-controls {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
}

/* === 🔥 优化：全屏主内容区域 === */
.main-content {
    flex: 1;
    display: flex;
    width: 100%;
    margin: 0;
}

/* === 侧边导航 === */
.sidebar {
    width: var(--sidebar-width);
    background: var(--bg-card);
    border-right: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    position: sticky;
    top: var(--header-height);
    height: calc(100vh - var(--header-height));
    overflow-y: auto;
    transition: width var(--transition-normal);
}

.nav-menu {
    flex: 1;
    padding: var(--spacing-4);
}

.nav-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    padding: var(--spacing-3) var(--spacing-4);
    margin-bottom: var(--spacing-1);
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: all var(--transition-fast);
    color: var(--text-secondary);
    font-weight: 500;
}

.nav-item:hover {
    background: var(--bg-secondary);
    color: var(--text-primary);
}

.nav-item.active {
    background: var(--primary-light);
    color: var(--primary-color);
    font-weight: 600;
}

.nav-icon {
    font-size: var(--font-size-lg);
    min-width: 20px;
    text-align: center;
}

.nav-text {
    font-size: var(--font-size-sm);
}

.sidebar-footer {
    padding: var(--spacing-4);
    border-top: 1px solid var(--border-color);
}

.system-info {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-2);
}

.info-item {
    display: flex;
    justify-content: space-between;
    font-size: var(--font-size-xs);
    color: var(--text-muted);
}

.info-item .label {
    font-weight: 500;
}

/* === 🔥 优化：全屏内容区域 === */
.content-area {
    flex: 1;
    padding: 12px;
    overflow-y: auto;
    max-height: calc(100vh - var(--header-height));
}

.page-content {
    display: none;
    animation: fadeIn var(--transition-fast);
}

.page-content.active {
    display: block;
}

.page-header {
    margin-bottom: var(--spacing-8);
}

.page-header h2 {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-2);
}

.page-header p {
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
}

/* === 统计卡片网格 === */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-6);
    margin-bottom: var(--spacing-8);
}

.stat-card {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-6);
    display: flex;
    align-items: center;
    gap: var(--spacing-4);
    transition: all var(--transition-fast);
    position: relative;
    overflow: hidden;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
}

.stat-icon {
    font-size: var(--font-size-4xl);
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--bg-secondary);
    border-radius: var(--border-radius-lg);
    flex-shrink: 0;
}

.stat-content {
    flex: 1;
}

.stat-content h3 {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-1);
}

.stat-content p {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-2);
}

.stat-change {
    display: flex;
    align-items: center;
    gap: var(--spacing-1);
    font-size: var(--font-size-xs);
    font-weight: 600;
}

.stat-change.positive {
    color: var(--success-color);
}

.stat-change.negative {
    color: var(--error-color);
}

.stat-change.neutral {
    color: var(--text-muted);
}

/* === 仪表盘网格 === */
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: var(--spacing-6);
}

.dashboard-card {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
    transition: all var(--transition-fast);
}

.dashboard-card:hover {
    box-shadow: var(--shadow-md);
}

.card-header {
    padding: var(--spacing-4) var(--spacing-6);
    border-bottom: 1px solid var(--border-color);
    background: var(--bg-secondary);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.card-header h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.card-content {
    padding: var(--spacing-6);
}

/* === 加载和动画 === */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--bg-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: var(--z-modal);
    transition: opacity var(--transition-normal);
}

.loading-spinner {
    text-align: center;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 3px solid var(--border-color);
    border-top: 3px solid var(--primary-color);
    border-radius: var(--border-radius-full);
    animation: spin 1s linear infinite;
    margin: 0 auto var(--spacing-4);
}

.loading-spinner p {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

/* === 动画定义 === */
@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

@keyframes pulse {

    0%,
    100% {
        opacity: 1;
    }

    50% {
        opacity: 0.5;
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
    }

    to {
        transform: translateX(0);
    }
}

@keyframes bounce {

    0%,
    20%,
    53%,
    80%,
    100% {
        transform: translate3d(0, 0, 0);
    }

    40%,
    43% {
        transform: translate3d(0, -8px, 0);
    }

    70% {
        transform: translate3d(0, -4px, 0);
    }

    90% {
        transform: translate3d(0, -2px, 0);
    }
}

/* === 实用工具类 === */
.text-center {
    text-align: center;
}

.text-left {
    text-align: left;
}

.text-right {
    text-align: right;
}

.d-flex {
    display: flex;
}

.d-block {
    display: block;
}

.d-inline-block {
    display: inline-block;
}

.d-none {
    display: none;
}

.align-center {
    align-items: center;
}

.justify-center {
    justify-content: center;
}

.justify-between {
    justify-content: space-between;
}

.w-full {
    width: 100%;
}

.h-full {
    height: 100%;
}

.mt-auto {
    margin-top: auto;
}

.ml-auto {
    margin-left: auto;
}

.mr-auto {
    margin-right: auto;
}

.p-0 {
    padding: 0;
}

.p-1 {
    padding: var(--spacing-1);
}

.p-2 {
    padding: var(--spacing-2);
}

.p-3 {
    padding: var(--spacing-3);
}

.p-4 {
    padding: var(--spacing-4);
}

.m-0 {
    margin: 0;
}

.m-1 {
    margin: var(--spacing-1);
}

.m-2 {
    margin: var(--spacing-2);
}

.m-3 {
    margin: var(--spacing-3);
}

.m-4 {
    margin: var(--spacing-4);
}

.rounded {
    border-radius: var(--border-radius);
}

.rounded-lg {
    border-radius: var(--border-radius-lg);
}

.rounded-full {
    border-radius: var(--border-radius-full);
}

.shadow-sm {
    box-shadow: var(--shadow-sm);
}

.shadow-md {
    box-shadow: var(--shadow-md);
}

.shadow-lg {
    box-shadow: var(--shadow-lg);
}

/* === 文本颜色工具类 === */
.text-primary {
    color: var(--text-primary);
}

.text-secondary {
    color: var(--text-secondary);
}

.text-muted {
    color: var(--text-muted);
}

.text-success {
    color: var(--success-color);
}

.text-warning {
    color: var(--warning-color);
}

.text-error {
    color: var(--error-color);
}

.text-info {
    color: var(--info-color);
}