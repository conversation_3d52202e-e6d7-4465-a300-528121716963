/**
 * 🚀 完整系统启动脚本
 * 一键启动所有服务和测试环境
 */

const express = require('express');
const cors = require('cors');
const path = require('path');

console.log('🎉 启动DLMM完整系统...');
console.log('=====================================');

// 创建Express应用
const app = express();
const API_PORT = 7000;

// 中间件配置
app.use(cors({
    origin: '*',
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization']
}));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// 静态文件服务
app.use('/test', express.static(path.join(__dirname, 'web/public/test')));
app.use('/js', express.static(path.join(__dirname, 'web/public/js')));
app.use('/css', express.static(path.join(__dirname, 'web/public/css')));
app.use('/assets', express.static(path.join(__dirname, 'web/public/assets')));
app.use('/', express.static(path.join(__dirname, 'web/public')));

// 系统状态
let systemStats = {
    startTime: Date.now(),
    requestCount: 0,
    errorCount: 0,
    strategies: [],
    connections: 0
};

// 请求计数中间件
app.use((req, res, next) => {
    systemStats.requestCount++;
    console.log(`📡 ${new Date().toLocaleTimeString()} ${req.method} ${req.path}`);
    next();
});

// ==================== API 路由 ====================

// 健康检查
app.get('/api/health', (req, res) => {
    const uptime = Date.now() - systemStats.startTime;
    const memoryUsage = process.memoryUsage();
    
    res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: uptime,
        message: '🎉 DLMM系统运行正常',
        services: {
            api: 'healthy',
            web: 'healthy',
            database: 'file-based',
            jupiter: 'connected',
            meteora: 'connected',
            solana: 'mainnet-beta'
        },
        stats: {
            totalRequests: systemStats.requestCount,
            errorRequests: systemStats.errorCount,
            successRate: systemStats.requestCount > 0 ? 
                ((systemStats.requestCount - systemStats.errorCount) / systemStats.requestCount * 100).toFixed(2) + '%' : '100%',
            activeStrategies: systemStats.strategies.length,
            connections: systemStats.connections
        },
        memory: {
            used: Math.round(memoryUsage.heapUsed / 1024 / 1024) + 'MB',
            total: Math.round(memoryUsage.heapTotal / 1024 / 1024) + 'MB',
            external: Math.round(memoryUsage.external / 1024 / 1024) + 'MB'
        },
        version: '1.0.0-complete'
    });
});

// 系统指标
app.get('/api/metrics', (req, res) => {
    const uptime = Date.now() - systemStats.startTime;
    const memoryUsage = process.memoryUsage();
    
    res.json({
        status: 'ok',
        timestamp: new Date().toISOString(),
        uptime: {
            ms: uptime,
            seconds: Math.floor(uptime / 1000),
            formatted: formatUptime(uptime)
        },
        requests: {
            total: systemStats.requestCount,
            successful: systemStats.requestCount - systemStats.errorCount,
            failed: systemStats.errorCount,
            successRate: systemStats.requestCount > 0 ? 
                ((systemStats.requestCount - systemStats.errorCount) / systemStats.requestCount * 100) : 100
        },
        memory: {
            heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024),
            heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024),
            external: Math.round(memoryUsage.external / 1024 / 1024),
            rss: Math.round(memoryUsage.rss / 1024 / 1024)
        },
        system: {
            platform: process.platform,
            arch: process.arch,
            nodeVersion: process.version,
            pid: process.pid
        },
        services: {
            jupiter: { 
                status: 'healthy', 
                version: 'v7',
                endpoint: 'https://lite-api.jup.ag',
                requestCount: Math.floor(Math.random() * 100),
                avgResponseTime: Math.floor(Math.random() * 500) + 100
            },
            meteora: { 
                status: 'healthy',
                endpoint: 'https://dlmm-api.meteora.ag',
                poolCount: Math.floor(Math.random() * 50) + 20
            },
            solana: { 
                status: 'healthy', 
                network: 'mainnet-beta',
                slot: Math.floor(Math.random() * 1000000) + 200000000,
                tps: Math.floor(Math.random() * 3000) + 1000
            }
        }
    });
});

// 策略管理
app.get('/api/strategy', (req, res) => {
    res.json({
        success: true,
        data: systemStats.strategies,
        total: systemStats.strategies.length,
        message: '策略列表获取成功'
    });
});

app.post('/api/strategy', (req, res) => {
    const newStrategy = {
        id: 'strategy_' + Date.now(),
        name: req.body.name || '新策略',
        type: req.body.type || 'simple_y',
        status: 'active',
        createdAt: new Date().toISOString(),
        ...req.body
    };
    
    systemStats.strategies.push(newStrategy);
    
    res.json({
        success: true,
        data: newStrategy,
        message: '策略创建成功'
    });
});

// Jupiter API模拟
app.get('/api/jupiter/health', (req, res) => {
    res.json({
        success: true,
        data: {
            status: 'healthy',
            message: 'Jupiter V7 API连接正常',
            version: 'v7',
            endpoint: 'https://lite-api.jup.ag/ultra/v1',
            lastCheck: new Date().toISOString()
        }
    });
});

app.get('/api/jupiter/metrics', (req, res) => {
    res.json({
        success: true,
        data: {
            requestCount: Math.floor(Math.random() * 1000) + 500,
            errorCount: Math.floor(Math.random() * 10),
            successCount: Math.floor(Math.random() * 990) + 490,
            successRate: 98.5 + Math.random() * 1.5,
            avgResponseTime: Math.floor(Math.random() * 300) + 100,
            version: 'ultra-v1',
            endpoint: 'https://lite-api.jup.ag/ultra/v1'
        }
    });
});

// 工具函数
function formatUptime(ms) {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) {
        return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
    } else if (minutes > 0) {
        return `${minutes}m ${seconds % 60}s`;
    } else {
        return `${seconds}s`;
    }
}

// 启动服务器
app.listen(API_PORT, () => {
    console.log('');
    console.log('🎉 DLMM完整系统启动成功！');
    console.log('=====================================');
    console.log(`🌐 API服务器: http://localhost:${API_PORT}`);
    console.log(`❤️ 健康检查: http://localhost:${API_PORT}/api/health`);
    console.log(`📊 系统指标: http://localhost:${API_PORT}/api/metrics`);
    console.log(`🧪 API测试: http://localhost:${API_PORT}/test/test-api-integration.html`);
    console.log(`🎯 独立测试: http://localhost:${API_PORT}/standalone-test.html`);
    console.log('');
    console.log('💡 系统功能:');
    console.log('  ✅ API服务器运行');
    console.log('  ✅ 健康检查可用');
    console.log('  ✅ 指标监控可用');
    console.log('  ✅ 策略管理可用');
    console.log('  ✅ Jupiter API模拟');
    console.log('  ✅ 静态文件服务');
    console.log('');
    console.log('🚀 现在可以开始测试了！');
});

// 优雅关闭
process.on('SIGINT', () => {
    console.log('\n🛑 正在关闭服务器...');
    process.exit(0);
});
