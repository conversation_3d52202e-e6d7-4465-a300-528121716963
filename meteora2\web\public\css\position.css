/**
 * 💼 头寸管理页面样式
 * 包含头寸列表、摘要卡片、工具栏等所有头寸管理相关样式
 */

/* 主容器 */
.position-ui {
    padding: 0;
    width: 100%;
}

/* 头寸摘要统计 */
.position-summary {
    margin-bottom: 2rem;
}

.summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.summary-card {
    background: var(--card-bg);
    border-radius: 12px;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.summary-card:hover {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.summary-card .card-icon {
    font-size: 2.5rem;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--primary-color-light);
    border-radius: 50%;
    color: var(--primary-color);
}

.summary-card .card-content h3 {
    font-size: 1.8rem;
    font-weight: 700;
    margin: 0 0 0.25rem 0;
    color: var(--text-primary);
}

.summary-card .card-content p {
    font-size: 0.9rem;
    color: var(--text-secondary);
    margin: 0;
}

/* 操作工具栏 */
.position-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: var(--card-bg);
    border-radius: 12px;
    border: 1px solid var(--border-color);
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.toolbar-left {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.toolbar-right {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.filter-group,
.sort-group {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.filter-group label,
.sort-group label {
    font-size: 0.9rem;
    color: var(--text-secondary);
    white-space: nowrap;
}

.form-select {
    padding: 0.5rem 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    background: var(--bg-secondary);
    color: var(--text-primary);
    font-size: 0.9rem;
    min-width: 120px;
}

.form-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(var(--primary-color-rgb), 0.1);
}

/* 头寸列表容器 */
.position-list-container {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    overflow: hidden;
}

.position-list {
    min-height: 200px;
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    color: var(--text-secondary);
}

.empty-state .empty-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
}

.empty-state h3 {
    font-size: 1.5rem;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.empty-state p {
    font-size: 1rem;
    margin-bottom: 2rem;
    opacity: 0.8;
}

/* 加载状态 */
.loading-placeholder {
    text-align: center;
    padding: 3rem 2rem;
    color: var(--text-secondary);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
}

.loading-placeholder .spinner {
    width: 32px;
    height: 32px;
    border: 3px solid var(--border-color);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

/* 头寸项 */
.position-item {
    border-bottom: 1px solid var(--border-color);
    padding: 1.5rem;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.position-item:hover {
    background: var(--hover-bg);
}

.position-item:last-child {
    border-bottom: none;
}

.position-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.position-info {
    flex: 1;
}

.position-info h4 {
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0 0 0.25rem 0;
    color: var(--text-primary);
}

.position-info p {
    font-size: 0.9rem;
    color: var(--text-secondary);
    margin: 0;
}

.position-actions {
    display: flex;
    gap: 0.5rem;
}

.position-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
}

.stat-item {
    text-align: center;
    padding: 0.75rem;
    background: var(--bg-secondary);
    border-radius: 8px;
}

.stat-value {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.stat-label {
    font-size: 0.8rem;
    color: var(--text-secondary);
}

/* 头寸详情 */
.position-details {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid var(--border-color);
    display: none;
}

.position-details-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.detail-section h4 {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
    color: var(--text-primary);
}

.liquidity-chart,
.earning-details {
    background: var(--bg-secondary);
    padding: 1rem;
    border-radius: 8px;
}

.earning-item {
    padding: 0.5rem 0;
    border-bottom: 1px solid var(--border-color);
}

.earning-item:last-child {
    border-bottom: none;
}

/* 状态标识 */
.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
    text-transform: uppercase;
}

.status-active {
    background: rgba(34, 197, 94, 0.1);
    color: #22c55e;
}

.status-closed {
    background: rgba(156, 163, 175, 0.1);
    color: #9ca3af;
}

.status-empty {
    background: rgba(251, 191, 36, 0.1);
    color: #fbbf24;
}

/* 按钮样式补充 */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    font-size: 0.9rem;
    font-weight: 500;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background: var(--primary-color-dark);
    transform: translateY(-1px);
}

.btn-outline {
    background: transparent;
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.btn-outline:hover:not(:disabled) {
    background: var(--hover-bg);
    border-color: var(--primary-color);
}

.btn-sm {
    padding: 0.35rem 0.75rem;
    font-size: 0.8rem;
}

.btn-danger {
    background: #ef4444;
    color: white;
}

.btn-danger:hover:not(:disabled) {
    background: #dc2626;
}

.btn-success {
    background: #22c55e;
    color: white;
}

.btn-success:hover:not(:disabled) {
    background: #16a34a;
}

.btn-icon {
    font-size: 1em;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .summary-cards {
        grid-template-columns: 1fr;
    }

    .position-toolbar {
        flex-direction: column;
        align-items: stretch;
    }

    .toolbar-left,
    .toolbar-right {
        justify-content: center;
    }

    .position-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .position-actions {
        align-self: stretch;
        justify-content: center;
    }

    .position-stats {
        grid-template-columns: 1fr;
    }

    .position-details-content {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .summary-card {
        padding: 1rem;
    }

    .summary-card .card-icon {
        width: 50px;
        height: 50px;
        font-size: 2rem;
    }

    .summary-card .card-content h3 {
        font-size: 1.5rem;
    }

    .position-item {
        padding: 1rem;
    }

    .empty-state {
        padding: 3rem 1rem;
    }

    .empty-state .empty-icon {
        font-size: 3rem;
    }
}

/* 创建头寸表单样式 */
.create-position-form {
    max-width: 500px;
    margin: 0 auto;
}

.create-position-form h4 {
    text-align: center;
    margin-bottom: 1.5rem;
    color: var(--text-primary);
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-primary);
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    background: var(--bg-secondary);
    color: var(--text-primary);
    font-size: 0.9rem;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(var(--primary-color-rgb), 0.1);
}

.form-help {
    display: block;
    margin-top: 0.25rem;
    font-size: 0.8rem;
    color: var(--text-secondary);
}

.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 2rem;
    padding-top: 1rem;
    border-top: 1px solid var(--border-color);
}

/* 模态框样式补充 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-container {
    background: var(--card-bg);
    border-radius: 12px;
    max-width: 90vw;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
    margin: 0;
    color: var(--text-primary);
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--text-secondary);
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-close:hover {
    background: var(--hover-bg);
    border-radius: 50%;
}

.modal-body {
    padding: 1.5rem;
}

.modal-backdrop {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

/* 响应式 - 表单 */
@media (max-width: 480px) {
    .create-position-form {
        max-width: 100%;
    }

    .form-actions {
        flex-direction: column;
    }

    .modal-container {
        margin: 1rem;
        max-width: calc(100vw - 2rem);
    }

    .modal-body {
        padding: 1rem;
    }
}

/* 横向头寸项布局 */
.position-item-horizontal {
    border-bottom: 1px solid var(--border-color);
    padding: 1rem 1.5rem;
    cursor: pointer;
    transition: background-color 0.2s ease;
    display: flex;
    align-items: center;
    gap: 1rem;
    min-height: 80px;
}

.position-item-horizontal:hover {
    background: var(--hover-bg);
}

.position-item-horizontal:last-child {
    border-bottom: none;
}

/* 基本信息区域 */
.position-basic-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    min-width: 220px;
    flex-shrink: 0;
}

.position-type-badge {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    width: fit-content;
}

.position-type-badge.x {
    background: rgba(239, 68, 68, 0.1);
    color: #ef4444;
    border: 1px solid rgba(239, 68, 68, 0.2);
}

.position-type-badge.y {
    background: rgba(34, 197, 94, 0.1);
    color: #22c55e;
    border: 1px solid rgba(34, 197, 94, 0.2);
}

.position-type-badge.undefined {
    background: rgba(156, 163, 175, 0.1);
    color: #9ca3af;
    border: 1px solid rgba(156, 163, 175, 0.2);
}

.pool-pair {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0.25rem 0;
}

.position-address {
    font-size: 0.85rem;
    color: var(--text-secondary);
    font-family: 'Courier New', monospace;
}

/* 横向指标区域 */
.position-metrics-horizontal {
    display: flex;
    gap: 2rem;
    flex: 1;
    justify-content: space-between;
    align-items: center;
    min-width: 0;
}

.metric-cell {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.25rem;
    min-width: 0;
    flex: 1;
}

.metric-cell .metric-label {
    font-size: 0.8rem;
    color: var(--text-secondary);
    text-align: center;
    white-space: nowrap;
}

.metric-cell .metric-value {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
    text-align: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
}

.metric-cell .metric-value.positive {
    color: #22c55e;
}

.metric-cell .metric-value.negative {
    color: #ef4444;
}

/* 状态区域 */
.position-status-section {
    display: flex;
    align-items: center;
    min-width: 80px;
    justify-content: center;
}

.position-status {
    padding: 0.35rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
    text-transform: uppercase;
    white-space: nowrap;
}

.position-status.active {
    background: rgba(34, 197, 94, 0.1);
    color: #22c55e;
    border: 1px solid rgba(34, 197, 94, 0.2);
}

.position-status.inactive {
    background: rgba(156, 163, 175, 0.1);
    color: #9ca3af;
    border: 1px solid rgba(156, 163, 175, 0.2);
}

/* 横向操作按钮区域 */
.position-actions-horizontal {
    display: flex;
    gap: 0.5rem;
    flex-shrink: 0;
    min-width: 200px;
    justify-content: flex-end;
}

.position-actions-horizontal .btn {
    padding: 0.4rem 0.8rem;
    font-size: 0.8rem;
    white-space: nowrap;
}

/* 响应式设计：在小屏幕上回退到垂直布局 */
@media (max-width: 1200px) {
    .position-metrics-horizontal {
        gap: 1rem;
    }

    .metric-cell .metric-label,
    .metric-cell .metric-value {
        font-size: 0.85rem;
    }
}

@media (max-width: 992px) {
    .position-item-horizontal {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
        min-height: auto;
        padding: 1.5rem;
    }

    .position-basic-info {
        min-width: auto;
        flex-direction: row;
        align-items: center;
        gap: 1rem;
        flex-wrap: wrap;
    }

    .position-metrics-horizontal {
        justify-content: space-around;
        gap: 0.5rem;
    }

    .position-status-section {
        min-width: auto;
        justify-content: flex-start;
    }

    .position-actions-horizontal {
        min-width: auto;
        justify-content: center;
        flex-wrap: wrap;
    }
}

@media (max-width: 640px) {
    .position-item-horizontal {
        padding: 1rem;
    }

    .position-basic-info {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .position-metrics-horizontal {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
        justify-content: stretch;
    }

    .metric-cell {
        align-items: flex-start;
    }

    .position-actions-horizontal .btn {
        flex: 1;
        min-width: 80px;
    }
}