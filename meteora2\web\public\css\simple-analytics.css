/**
 * 📊 简化数据分析模块样式
 */

/* 🎨 主容器 */
.analytics-container {
    padding: 20px;
    background: var(--bg-color);
    border-radius: 12px;
    margin-bottom: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* 📱 标题栏 */
.analytics-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid var(--border-color);
}

.analytics-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-color);
    margin: 0;
}

.analytics-controls {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-bottom: 25px;
    padding: 15px;
    background: var(--card-bg);
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

/* 🕐 时间范围选择器 */
.time-range-selector {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.time-range-label {
    font-size: 0.9rem;
    font-weight: 500;
    color: var(--text-color);
}

.time-range-buttons {
    display: flex;
    gap: 8px;
}

.time-range-btn {
    padding: 8px 16px;
    border: 1px solid var(--border-color);
    background: var(--card-bg);
    color: var(--text-color);
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: all 0.2s ease;
}

.time-range-btn:hover {
    background: var(--accent-color);
    color: white;
    border-color: var(--accent-color);
}

.time-range-btn.active {
    background: var(--accent-color);
    color: white;
    border-color: var(--accent-color);
    font-weight: 600;
}

/* 📋 数据类型选择器 */
.data-type-selector {
    display: flex;
    flex-direction: column;
    gap: 8px;
    flex: 1;
}

.data-type-label {
    font-size: 0.9rem;
    font-weight: 500;
    color: var(--text-color);
}

.data-type-options {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
}

.data-type-option {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 6px 12px;
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.data-type-option:hover {
    background: var(--accent-color-light);
    border-color: var(--accent-color);
}

.data-type-option input[type="checkbox"] {
    margin: 0;
    cursor: pointer;
}

.data-type-option label {
    font-size: 0.9rem;
    color: var(--text-color);
    cursor: pointer;
    margin: 0;
}

/* 🔧 操作按钮 */
.analytics-actions {
    display: flex;
    gap: 10px;
}

.btn-refresh {
    padding: 8px 16px;
    background: var(--success-color);
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: all 0.2s ease;
}

.btn-refresh:hover {
    background: var(--success-color-dark);
}

/* 📊 图表容器 */
.charts-container {
    display: flex;
    flex-direction: column;
    gap: 25px;
}

.chart-row {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.2s ease;
}

.chart-row:hover {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background: var(--bg-color);
    border-bottom: 1px solid var(--border-color);
    flex-wrap: wrap;
    gap: 10px;
}

.strategy-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-color);
    margin: 0;
    flex: 1;
}

.strategy-id {
    font-size: 0.8rem;
    color: var(--text-secondary);
    font-family: 'Courier New', monospace;
    background: var(--card-bg);
    padding: 4px 8px;
    border-radius: 4px;
    border: 1px solid var(--border-color);
}

.chart-legend {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 0.9rem;
}

.legend-color {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.legend-label {
    color: var(--text-color);
    font-weight: 500;
}

.chart-wrapper {
    position: relative;
    height: 350px;
    padding: 20px;
    background: var(--card-bg);
}

.strategy-chart {
    width: 100% !important;
    height: 100% !important;
}

/* 📊 状态显示 */
.chart-empty,
.chart-error {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 310px;
    text-align: center;
    color: var(--text-secondary);
}

.chart-empty .empty-icon,
.chart-error .error-icon {
    font-size: 3rem;
    margin-bottom: 15px;
}

.chart-empty p,
.chart-error p {
    font-size: 1.1rem;
    font-weight: 500;
    margin: 0 0 8px 0;
    color: var(--text-color);
}

.chart-empty small,
.chart-error small {
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.chart-error {
    color: var(--error-color);
}

.chart-error .error-icon {
    color: var(--error-color);
}

/* 📈 状态信息栏 */
.analytics-status {
    display: flex;
    gap: 20px;
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.status-item {
    display: flex;
    align-items: center;
    gap: 5px;
}

.status-label {
    font-weight: 500;
}

.status-value {
    color: var(--text-color);
    font-weight: 600;
}

/* ❌ 错误状态 */
.analytics-error {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 50px;
    text-align: center;
    background: var(--card-bg);
    border-radius: 8px;
    border: 1px solid var(--error-color);
}

.analytics-error .error-icon {
    font-size: 4rem;
    color: var(--error-color);
    margin-bottom: 20px;
}

.analytics-error h3 {
    color: var(--error-color);
    margin: 0 0 10px 0;
}

.analytics-error p {
    color: var(--text-secondary);
    margin: 0 0 20px 0;
}

.analytics-error button {
    padding: 10px 20px;
    background: var(--error-color);
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 1rem;
}

.analytics-error button:hover {
    background: var(--error-color-dark);
}

/* 📱 响应式设计 */
@media (max-width: 768px) {
    .analytics-controls {
        flex-direction: column;
        gap: 15px;
    }

    .analytics-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }

    .analytics-status {
        flex-direction: column;
        gap: 10px;
    }

    .time-range-buttons {
        flex-wrap: wrap;
    }

    .data-type-options {
        flex-direction: column;
    }

    .chart-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .chart-wrapper {
        height: 250px;
        padding: 15px;
    }

    .chart-empty,
    .chart-error {
        height: 210px;
    }
}

/* 加载状态 */
.loading-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 50px;
    text-align: center;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--border-color);
    border-top: 4px solid var(--accent-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 15px;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

.loading-state p {
    color: var(--text-secondary);
    font-size: 1rem;
    margin: 0;
}

/* 空状态 */
.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 50px;
    text-align: center;
}

.empty-icon {
    font-size: 4rem;
    margin-bottom: 20px;
}

.empty-state h4 {
    font-size: 1.3rem;
    color: var(--text-color);
    margin: 0 0 10px 0;
}

.empty-state p {
    color: var(--text-secondary);
    font-size: 1rem;
    margin: 0 0 20px 0;
    max-width: 400px;
}

.empty-state .btn-refresh {
    margin-top: 10px;
}

/* 错误状态 */
.error-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 50px;
    text-align: center;
}

.error-icon {
    font-size: 4rem;
    color: var(--error-color);
    margin-bottom: 20px;
}

.error-state h4 {
    font-size: 1.3rem;
    color: var(--error-color);
    margin: 0 0 10px 0;
}

.error-state p {
    color: var(--text-secondary);
    font-size: 1rem;
    margin: 0 0 20px 0;
    max-width: 400px;
}

/* 主题变量定义 */
:root {
    --bg-color: #ffffff;
    --card-bg: #f8fafc;
    --text-color: #1f2937;
    --text-secondary: #6b7280;
    --border-color: #e5e7eb;
    --accent-color: #3b82f6;
    --accent-color-light: rgba(59, 130, 246, 0.1);
    --success-color: #10b981;
    --success-color-dark: #059669;
    --error-color: #ef4444;
    --error-color-dark: #dc2626;
}

/* 暗色主题 */
[data-theme="dark"] {
    --bg-color: #1a1a1a;
    --card-bg: #2d2d2d;
    --text-color: #ffffff;
    --text-secondary: #b0b0b0;
    --border-color: #404040;
    --accent-color: #10b981;
    --accent-color-light: rgba(16, 185, 129, 0.1);
    --success-color: #10b981;
    --success-color-dark: #059669;
    --error-color: #ef4444;
    --error-color-dark: #dc2626;
}

/* 亮色主题 */
[data-theme="light"] {
    --bg-color: #ffffff;
    --card-bg: #f8fafc;
    --text-color: #1f2937;
    --text-secondary: #6b7280;
    --border-color: #e5e7eb;
    --accent-color: #3b82f6;
    --accent-color-light: rgba(59, 130, 246, 0.1);
    --success-color: #10b981;
    --success-color-dark: #059669;
    --error-color: #ef4444;
    --error-color-dark: #dc2626;
}

/* 浏览器偏好设置 */
@media (prefers-color-scheme: dark) {
    :root {
        --bg-color: #1a1a1a;
        --card-bg: #2d2d2d;
        --text-color: #ffffff;
        --text-secondary: #b0b0b0;
        --border-color: #404040;
        --accent-color: #10b981;
        --accent-color-light: rgba(16, 185, 129, 0.1);
        --success-color: #10b981;
        --success-color-dark: #059669;
        --error-color: #ef4444;
        --error-color-dark: #dc2626;
    }
}