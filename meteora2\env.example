# 🌐 DLMM流动性管理系统环境变量配置

# 运行环境
NODE_ENV=development

# 服务器配置
SERVER_PORT=7000
SERVER_HOST=0.0.0.0

# Solana配置
SOLANA_NETWORK=mainnet-beta
SOLANA_PRIVATE_KEY=your-solana-private-key-base58-here
RPC_PRIMARY=https://api.mainnet-beta.solana.com
RPC_BACKUP=https://solana-api.projectserum.com
RPC_HELIUS=https://rpc.helius.xyz/?api-key=your-helius-key

# Jupiter配置
JUPITER_API_URL=https://quote-api.jup.ag/v6
JUPITER_SLIPPAGE_BPS=50

# 交易配置
PRIORITY_FEE=5000
MAX_RETRIES=3
RETRY_DELAY_MS=2000

# 日志配置
LOG_LEVEL=info
LOG_MAX_FILE_SIZE=2097152
LOG_MAX_FILES=5

# 钱包加密
WALLET_ENCRYPTION_KEY=your-wallet-encryption-key-here

# WebSocket配置
WS_PORT=7002

# 监控配置
MONITOR_PORT=7003
HEALTH_CHECK_INTERVAL=30000

# 缓存配置
CACHE_TTL=300000

# 数据库配置
DATA_DIR=./data
STATE_DIR=./data/states
CONFIG_DIR=./config 