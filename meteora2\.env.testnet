# DLMM测试网环境配置
NODE_ENV=testnet
LOG_LEVEL=debug

# 🌐 Solana测试网配置
SOLANA_NETWORK=devnet
SOLANA_RPC_URL=https://api.devnet.solana.com
SOLANA_WS_URL=wss://api.devnet.solana.com
SOLANA_COMMITMENT=confirmed

# 🔑 钱包配置（测试用）
# 注意：这里使用测试钱包，不要放真实资金
WALLET_PRIVATE_KEY=your_test_wallet_private_key_here

# 🪐 Jupiter API（测试网）
JUPITER_API_URL=https://quote-api.jup.ag
JUPITER_VERSION=v6

# 🌊 Meteora API（测试网）
METEORA_API_URL=https://dlmm-api.meteora.ag
METEORA_NETWORK=devnet

# 📊 服务端口
API_PORT=7000
WS_PORT=7002
WEB_PORT=7001

# 💾 数据存储（本地文件）
DATA_STORAGE=file
DATA_PATH=./data/testnet
CACHE_TYPE=memory

# 📝 日志配置
LOG_FILE=./logs/dlmm-testnet.log
LOG_ROTATION=daily

# 🔧 测试网特殊配置
ENABLE_SIMULATION=true
DRY_RUN_MODE=false
TEST_MODE=true
SKIP_BALANCE_CHECK=false

# ⚡ 交易配置（测试网优化）
PRIORITY_FEE=1000
MAX_RETRIES=3
RETRY_DELAY=2000
TRANSACTION_TIMEOUT=30000

# 🛡️ 风险控制（测试网宽松设置）
MAX_SLIPPAGE_BPS=500
MIN_POOL_LIQUIDITY=1000
MAX_POSITION_SIZE=1000
