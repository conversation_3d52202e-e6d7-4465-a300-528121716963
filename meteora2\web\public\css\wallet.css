/**
 * 🎨 本地私钥钱包管理系统样式
 * 专门为本地私钥钱包管理设计的现代化界面
 */

/* ========== 基础容器 ========== */
.local-wallet-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: var(--bg-secondary);
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid var(--border-color);
}

/* ========== 钱包状态区域 ========== */
.wallet-status-section {
    margin-bottom: 2rem;
}

.wallet-status-card {
    background: var(--card-bg);
    border-radius: 12px;
    padding: 1.5rem;
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.wallet-status-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.status-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.status-header h3 {
    margin: 0;
    color: var(--text-primary);
    font-size: 1.2rem;
    font-weight: 600;
}

.status-content {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1rem;
    font-weight: 500;
}

.status-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    display: inline-block;
    animation: pulse 2s infinite;
}

.status-dot.no-wallet {
    background: #6b7280;
    animation: none;
}

.status-dot.locked {
    background: #f59e0b;
}

.status-dot.unlocked {
    background: #10b981;
}

.status-dot.disconnected {
    background: #ef4444;
    animation: none;
}

@keyframes pulse {

    0%,
    100% {
        opacity: 1;
    }

    50% {
        opacity: 0.5;
    }
}

.wallet-address-display {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem;
    background: var(--bg-tertiary);
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.address-label {
    color: var(--text-secondary);
    font-size: 0.9rem;
    font-weight: 500;
}

.address-text {
    font-family: 'Monaco', 'Consolas', monospace;
    font-size: 0.9rem;
    color: var(--text-primary);
    flex: 1;
}

.btn-copy-address {
    background: transparent;
    border: none;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    cursor: pointer;
    font-size: 1rem;
    transition: all 0.2s ease;
}

.btn-copy-address:hover {
    background: var(--bg-hover);
}

/* ========== 钱包操作区域 ========== */
.wallet-actions-section {
    margin-bottom: 2rem;
}

.wallet-actions-card {
    background: var(--card-bg);
    border-radius: 12px;
    padding: 1.5rem;
    border: 1px solid var(--border-color);
}

.actions-header h3 {
    margin: 0 0 1rem 0;
    color: var(--text-primary);
    font-size: 1.2rem;
    font-weight: 600;
}

.actions-content {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.no-wallet-actions,
.wallet-exists-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.locked-actions,
.unlocked-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

/* ========== 按钮样式 ========== */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-size: 0.9rem;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.2s ease;
    outline: none;
    white-space: nowrap;
}

.btn:focus {
    ring: 2px solid var(--accent-color);
    ring-offset: 2px;
}

.btn-primary {
    background: var(--accent-color);
    color: white;
}

.btn-primary:hover {
    background: var(--accent-hover);
    transform: translateY(-1px);
}

.btn-secondary {
    background: var(--bg-tertiary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.btn-secondary:hover {
    background: var(--bg-hover);
    border-color: var(--accent-color);
}

.btn-info {
    background: #3b82f6;
    color: white;
}

.btn-info:hover {
    background: #2563eb;
}

.btn-danger {
    background: #ef4444;
    color: white;
}

.btn-danger:hover {
    background: #dc2626;
}

.btn-icon {
    font-size: 1rem;
    opacity: 0.9;
}

/* ========== 钱包信息区域 ========== */
.wallet-info-section {
    margin-bottom: 2rem;
}

.wallet-info-card {
    background: var(--card-bg);
    border-radius: 12px;
    padding: 1.5rem;
    border: 1px solid var(--border-color);
}

.info-header h3 {
    margin: 0 0 1rem 0;
    color: var(--text-primary);
    font-size: 1.2rem;
    font-weight: 600;
}

.info-content {
    display: grid;
    gap: 0.75rem;
}

.info-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid var(--border-light);
}

.info-row:last-child {
    border-bottom: none;
}

.info-label {
    color: var(--text-secondary);
    font-weight: 500;
    font-size: 0.9rem;
}

.info-value {
    color: var(--text-primary);
    font-weight: 500;
    font-size: 0.9rem;
    text-align: right;
}

/* ========== 余额区域 ========== */
.wallet-balance-section {
    margin-bottom: 2rem;
}

.wallet-balance-card {
    background: var(--card-bg);
    border-radius: 12px;
    padding: 1.5rem;
    border: 1px solid var(--border-color);
}

.balance-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.balance-header h3 {
    margin: 0;
    color: var(--text-primary);
    font-size: 1.2rem;
    font-weight: 600;
}

.last-update {
    color: var(--text-secondary);
    font-size: 0.8rem;
}

.balance-content {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.balance-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: var(--bg-tertiary);
    border-radius: 8px;
    border: 1px solid var(--border-color);
    transition: all 0.2s ease;
}

.balance-item:hover {
    background: var(--bg-hover);
    border-color: var(--accent-color);
}

.sol-balance {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
}

.balance-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.token-symbol {
    font-weight: 600;
    font-size: 1rem;
}

.token-name {
    font-size: 0.8rem;
    opacity: 0.8;
}

.balance-amount {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 0.25rem;
}

.amount {
    font-weight: 600;
    font-size: 1rem;
    font-family: 'Monaco', 'Consolas', monospace;
}

.fiat-value {
    font-size: 0.8rem;
    opacity: 0.8;
}

.no-tokens {
    text-align: center;
    color: var(--text-secondary);
    padding: 2rem;
    font-style: italic;
}

/* ========== 模态框样式 ========== */
.wallet-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1rem;
}

.modal-backdrop {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
}

.modal-content {
    position: relative;
    background: var(--card-bg);
    border-radius: 16px;
    width: 100%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    border: 1px solid var(--border-color);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
    margin: 0;
    color: var(--text-primary);
    font-size: 1.3rem;
    font-weight: 600;
}

.modal-close {
    background: transparent;
    border: none;
    font-size: 1.5rem;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 4px;
    line-height: 1;
}

.modal-close:hover {
    background: var(--bg-hover);
    color: var(--text-primary);
}

.modal-body {
    padding: 1.5rem;
}

/* ========== 表单样式 ========== */
.form-group {
    margin-bottom: 1.5rem;
}

.form-group:last-child {
    margin-bottom: 0;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
    font-weight: 500;
    font-size: 0.9rem;
}

.form-control {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    background: var(--bg-secondary);
    color: var(--text-primary);
    font-size: 0.9rem;
    transition: all 0.2s ease;
    box-sizing: border-box;
}

.form-control:focus {
    outline: none;
    border-color: var(--accent-color);
    ring: 2px solid var(--accent-color);
    ring-opacity: 0.3;
}

.form-control::placeholder {
    color: var(--text-secondary);
    opacity: 0.7;
}

textarea.form-control {
    resize: vertical;
    min-height: 80px;
}

.form-hint {
    margin-top: 0.5rem;
    font-size: 0.8rem;
    color: var(--text-secondary);
    line-height: 1.4;
}

.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 2rem;
    padding-top: 1rem;
    border-top: 1px solid var(--border-color);
}

/* ========== 导入方式切换 ========== */
.import-type-tabs {
    display: flex;
    margin-bottom: 1.5rem;
    background: var(--bg-tertiary);
    border-radius: 8px;
    padding: 0.25rem;
}

.tab-btn {
    flex: 1;
    padding: 0.75rem 1rem;
    background: transparent;
    border: none;
    border-radius: 6px;
    color: var(--text-secondary);
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.tab-btn.active {
    background: var(--accent-color);
    color: white;
}

.tab-btn:hover:not(.active) {
    background: var(--bg-hover);
    color: var(--text-primary);
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* ========== 警告消息 ========== */
.warning-message {
    background: linear-gradient(135deg, #fef3c7 0%, #fcd34d 100%);
    border: 1px solid #f59e0b;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1.5rem;
    color: #92400e;
}

.warning-message p {
    margin: 0 0 0.5rem 0;
}

.warning-message p:last-child {
    margin-bottom: 0;
}

.warning-message strong {
    color: #d97706;
}

/* ========== 加载指示器 ========== */
.wallet-loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
}

.loading-content {
    background: var(--card-bg);
    border-radius: 12px;
    padding: 2rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    border: 1px solid var(--border-color);
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid var(--border-color);
    border-top: 3px solid var(--accent-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

.loading-text {
    color: var(--text-primary);
    font-weight: 500;
    text-align: center;
}

/* ========== 响应式设计 ========== */
@media (max-width: 768px) {
    .local-wallet-container {
        padding: 1rem;
        margin: 1rem;
    }

    .no-wallet-actions,
    .wallet-exists-actions,
    .locked-actions,
    .unlocked-actions {
        flex-direction: column;
    }

    .btn {
        justify-content: center;
        width: 100%;
    }

    .form-actions {
        flex-direction: column-reverse;
    }

    .modal-content {
        margin: 1rem;
        max-width: calc(100vw - 2rem);
    }

    .wallet-address-display {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.75rem;
    }

    .balance-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.75rem;
    }

    .balance-amount {
        align-items: flex-start;
        width: 100%;
    }
}

@media (max-width: 480px) {
    .local-wallet-container {
        padding: 0.75rem;
        margin: 0.5rem;
    }

    .wallet-status-card,
    .wallet-actions-card,
    .wallet-info-card,
    .wallet-balance-card {
        padding: 1rem;
    }

    .import-type-tabs {
        flex-direction: column;
        gap: 0.25rem;
    }

    .tab-btn {
        text-align: center;
    }
}

/* ========== 暗色主题支持 ========== */
@media (prefers-color-scheme: dark) {
    .sol-balance {
        background: linear-gradient(135deg, #4c1d95 0%, #581c87 100%);
    }

    .warning-message {
        background: linear-gradient(135deg, #451a03 0%, #92400e 100%);
        border-color: #d97706;
        color: #fbbf24;
    }

    .warning-message strong {
        color: #fcd34d;
    }
}

/* ========== 动画增强 ========== */
.balance-item {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.wallet-modal {
    animation: modalIn 0.3s ease-out;
}

@keyframes modalIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }

    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* ========== 辅助功能 ========== */
.btn:focus-visible {
    outline: 2px solid var(--accent-color);
    outline-offset: 2px;
}

.form-control:focus-visible {
    outline: 2px solid var(--accent-color);
    outline-offset: 1px;
}

/* ========== 打印样式 ========== */
@media print {

    .wallet-modal,
    .wallet-loading-overlay,
    .btn,
    .form-actions {
        display: none !important;
    }

    .local-wallet-container {
        box-shadow: none;
        border: 1px solid #000;
    }
}