{"name": "dlmm-liquidity-manager", "version": "1.0.0", "description": "DLMM流动性管理系统 - 基于Solana的DLMM流动性自动化管理", "main": "dist/app.js", "scripts": {"build": "tsc", "start": "node dist/app.js", "dev": "npx ts-node src/app.ts", "dev:server": "npx ts-node src/app.ts", "dev:api": "npx ts-node src/app.ts", "dev:web": "cd web && npm run dev", "test": "jest", "lint": "eslint src/**/*.ts", "format": "prettier --write src/**/*.ts"}, "keywords": ["solana", "dlmm", "liquidity", "defi", "trading", "meteora", "jupiter"], "author": "DLMM Team", "license": "MIT", "dependencies": {"@coral-xyz/anchor": "^0.31.1", "@meteora-ag/dlmm": "^1.5.4", "@solana/wallet-adapter-base": "^0.9.23", "@solana/wallet-adapter-phantom": "^0.9.24", "@solana/web3.js": "^1.87.6", "@types/cheerio": "^0.22.35", "axios": "^1.9.0", "bcryptjs": "^2.4.3", "big.js": "^6.2.1", "bs58": "^6.0.0", "cheerio": "^1.1.0", "compression": "^1.7.4", "cors": "^2.8.5", "decimal.js": "^10.4.3", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "inversify": "^7.5.2", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "moment": "^2.29.4", "reflect-metadata": "^0.2.2", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "tsyringe": "^4.10.0", "uuid": "^9.0.1", "winston": "^3.11.0", "ws": "^8.14.2", "zod": "^3.22.4"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.8", "@types/joi": "^17.2.3", "@types/jsonwebtoken": "^9.0.5", "@types/lodash": "^4.14.202", "@types/node": "^20.10.5", "@types/uuid": "^9.0.7", "@types/ws": "^8.5.10", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "eslint": "^8.56.0", "jest": "^29.7.0", "nodemon": "^3.0.2", "prettier": "^3.1.1", "puppeteer": "^21.11.0", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "typescript": "^5.3.3"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-org/dlmm-liquidity-manager.git"}, "bugs": {"url": "https://github.com/your-org/dlmm-liquidity-manager/issues"}, "homepage": "https://github.com/your-org/dlmm-liquidity-manager#readme"}