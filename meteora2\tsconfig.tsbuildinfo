{"root": ["./src/app.ts", "./src/di/container.ts", "./src/infrastructure/configservice.ts", "./src/infrastructure/eventbus.ts", "./src/infrastructure/stateservice.ts", "./src/infrastructure/logging/logwriter.ts", "./src/infrastructure/logging/loggerservice.ts", "./src/infrastructure/logging/strategylogger.ts", "./src/infrastructure/logging/timeformatter.ts", "./src/infrastructure/logging/tracecontext.ts", "./src/infrastructure/logging/index.ts", "./src/server/api-server.ts", "./src/server/middleware/auth-middleware.ts", "./src/server/middleware/error-middleware.ts", "./src/server/middleware/logging-middleware.ts", "./src/server/middleware/validation-middleware.ts", "./src/server/routes/analytics-routes.ts", "./src/server/routes/chain-position-routes.ts", "./src/server/routes/config-routes.ts", "./src/server/routes/jupiter-routes.ts", "./src/server/routes/monitor-routes.ts", "./src/server/routes/pool-routes.ts", "./src/server/routes/position-routes.ts", "./src/server/routes/strategy-routes.ts", "./src/server/routes/wallet-routes.ts", "./src/server/websocket/websocket-handler.ts", "./src/server/websocket/websocket-server.ts", "./src/services/blockchain/gasservice.ts", "./src/services/blockchain/multirpcservice.ts", "./src/services/blockchain/solanaweb3service.ts", "./src/services/blockchain/walletservice.ts", "./src/services/business/chainpositionmanager.ts", "./src/services/business/liquidityoperationservice.ts", "./src/services/business/positionanalyticsservice.ts", "./src/services/business/positionfeeharvester.ts", "./src/services/business/positioninfoservice.ts", "./src/services/business/positionmanager.ts", "./src/services/business/xpositionmanager.ts", "./src/services/business/ypositionmanager.ts", "./src/services/business/analytics/lossanalyzer.ts", "./src/services/business/analytics/pnlcalculator.ts", "./src/services/business/analytics/pricemonitor.ts", "./src/services/business/analytics/unifieddataprovider.ts", "./src/services/business/analytics/yieldanalyzer.ts", "./src/services/business/analytics/yieldcalculator.ts", "./src/services/business/analytics/yieldoperator.ts", "./src/services/external/heliusservice.ts", "./src/services/external/jupiterservice.ts", "./src/services/external/meteoraservice.ts", "./src/services/internal/cacheservice.ts", "./src/services/internal/configservice.ts", "./src/services/internal/stateservice.ts", "./src/services/modules/chainpositiondataadapter.ts", "./src/services/modules/smartstoplossmodule.ts", "./src/services/strategy/strategymanager.ts", "./src/services/strategy/strategyregistry.ts", "./src/services/strategy/strategyscheduler.ts", "./src/services/strategy/executors/chainpositionexecutor.ts", "./src/services/strategy/executors/simpleyexecutor.ts", "./src/services/strategy/storage/strategystorage.ts", "./src/services/strategy/utils/simpleystateutils.ts", "./src/types/analytics-types.ts", "./src/types/interfaces.ts", "./src/types/logging.ts", "./src/types/strategy.ts", "./src/utils/servicehealthchecker.ts", "./src/utils/tokenprecisionconverter.ts"], "version": "5.8.3"}