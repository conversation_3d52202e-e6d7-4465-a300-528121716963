/* 🧩 DLMM流动性管理系统 - 组件样式文件 */
/* 包含所有UI组件的样式定义 */

/* ✅ 按钮组件样式 - 参考原始设计 */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-2);
    padding: var(--spacing-2) var(--spacing-4);
    border: 1px solid transparent;
    border-radius: var(--border-radius);
    font-size: var(--font-size-sm);
    font-weight: 500;
    line-height: 1.5;
    text-decoration: none;
    cursor: pointer;
    transition: all var(--transition-fast);
    position: relative;
    overflow: hidden;
    white-space: nowrap;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    pointer-events: none;
}

.btn:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* 按钮尺寸变体 */
.btn-sm {
    padding: var(--spacing-1) var(--spacing-3);
    font-size: var(--font-size-xs);
}

.btn-lg {
    padding: var(--spacing-3) var(--spacing-6);
    font-size: var(--font-size-base);
}

.btn-xl {
    padding: var(--spacing-4) var(--spacing-8);
    font-size: var(--font-size-lg);
}

/* 按钮样式变体 */
.btn-primary {
    background: var(--primary-color);
    color: var(--text-inverse);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background: var(--primary-hover);
    border-color: var(--primary-hover);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-secondary {
    background: var(--secondary-color);
    color: var(--text-inverse);
    border-color: var(--secondary-color);
}

.btn-secondary:hover {
    background: var(--text-primary);
    border-color: var(--text-primary);
    transform: translateY(-1px);
}

.btn-success {
    background: var(--success-color);
    color: var(--text-inverse);
    border-color: var(--success-color);
}

.btn-success:hover {
    background: #059669;
    border-color: #059669;
    transform: translateY(-1px);
}

.btn-warning {
    background: var(--warning-color);
    color: var(--text-inverse);
    border-color: var(--warning-color);
}

.btn-warning:hover {
    background: #d97706;
    border-color: #d97706;
    transform: translateY(-1px);
}

.btn-error {
    background: var(--error-color);
    color: var(--text-inverse);
    border-color: var(--error-color);
}

.btn-error:hover {
    background: #dc2626;
    border-color: #dc2626;
    transform: translateY(-1px);
}

.btn-outline {
    background: transparent;
    color: var(--text-primary);
    border-color: var(--border-color);
}

.btn-outline:hover {
    background: var(--bg-secondary);
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.btn-ghost {
    background: transparent;
    color: var(--text-secondary);
    border-color: transparent;
}

.btn-ghost:hover {
    background: var(--bg-secondary);
    color: var(--text-primary);
}

/* 图标按钮 */
.btn-icon {
    width: 40px;
    height: 40px;
    padding: 0;
    border-radius: var(--border-radius);
}

.btn-icon .icon {
    font-size: var(--font-size-lg);
}

/* 块级按钮 */
.btn-block {
    width: 100%;
}

/* ✅ 表单组件样式 */
.form-group {
    margin-bottom: var(--spacing-4);
}

.form-label {
    display: block;
    margin-bottom: var(--spacing-2);
    font-weight: 500;
    color: var(--text-primary);
    font-size: var(--font-size-sm);
}

.form-label.required::after {
    content: " *";
    color: var(--error-color);
}

.form-input,
.form-textarea,
.form-select {
    width: 100%;
    padding: var(--spacing-3);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: var(--font-size-sm);
    color: var(--text-primary);
    background: var(--bg-card);
    transition: all var(--transition-fast);
}

.form-input:focus,
.form-textarea:focus,
.form-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-input.error,
.form-textarea.error,
.form-select.error {
    border-color: var(--error-color);
}

.form-input.error:focus,
.form-textarea.error:focus,
.form-select.error:focus {
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.form-textarea {
    min-height: 120px;
    resize: vertical;
}

.form-help {
    font-size: var(--font-size-xs);
    color: var(--text-muted);
    margin-top: var(--spacing-1);
}

.form-error {
    font-size: var(--font-size-xs);
    color: var(--error-color);
    margin-top: var(--spacing-1);
}

/* 表单行布局 */
.form-row {
    display: flex;
    gap: var(--spacing-4);
    margin-bottom: var(--spacing-4);
}

.form-row .form-group {
    flex: 1;
    margin-bottom: 0;
}

/* ✅ 快速操作按钮 */
.quick-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: var(--spacing-3);
}

.action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-2);
    padding: var(--spacing-4);
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    color: var(--text-primary);
    text-decoration: none;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.action-btn:hover {
    background: var(--primary-light);
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.action-icon {
    font-size: var(--font-size-2xl);
    line-height: 1;
}

.action-text {
    font-size: var(--font-size-sm);
    font-weight: 500;
    text-align: center;
}

/* ✅ 活动列表样式 */
.activity-list {
    max-height: 300px;
    overflow-y: auto;
}

.activity-item {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-3);
    padding: var(--spacing-3);
    border-bottom: 1px solid var(--border-color-light);
    transition: background-color var(--transition-fast);
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-item:hover {
    background: var(--bg-secondary);
}

.activity-icon {
    font-size: var(--font-size-lg);
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
    flex-shrink: 0;
}

.activity-content {
    flex: 1;
    min-width: 0;
}

.activity-title {
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: var(--spacing-1);
}

.activity-time {
    font-size: var(--font-size-xs);
    color: var(--text-muted);
}

/* ✅ 系统指标样式 */
.system-metrics {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-4);
}

.metric-item {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-2);
}

.metric-label {
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
    font-weight: 500;
}

.metric-value {
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.metric-bar {
    height: 4px;
    background: var(--bg-secondary);
    border-radius: var(--border-radius-full);
    overflow: hidden;
}

.metric-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    border-radius: var(--border-radius-full);
    transition: width var(--transition-normal);
}

.metric-indicator {
    width: 8px;
    height: 8px;
    border-radius: var(--border-radius-full);
    background: var(--error-color);
}

.metric-indicator.connected {
    background: var(--success-color);
}

.metric-indicator.warning {
    background: var(--warning-color);
}

/* ✅ 图表控制按钮 */
.chart-controls {
    display: flex;
    gap: var(--spacing-1);
}

.chart-controls .btn {
    padding: var(--spacing-1) var(--spacing-2);
    font-size: var(--font-size-xs);
    border-radius: var(--border-radius-sm);
}

.chart-controls .btn.active {
    background: var(--primary-color);
    color: var(--text-inverse);
    border-color: var(--primary-color);
}

/* ✅ 模态框样式 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: var(--z-modal);
    display: none;
}

.modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
    animation: fadeIn var(--transition-normal);
}

.modal-backdrop {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--bg-overlay);
    cursor: pointer;
}

.modal-container {
    position: relative;
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    background: var(--bg-card);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-xl);
    overflow: hidden;
    animation: slideInUp var(--transition-normal);
}

.modal-header {
    padding: var(--spacing-4) var(--spacing-6);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.modal-header h3 {
    margin: 0;
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
}

.modal-close {
    background: none;
    border: none;
    font-size: var(--font-size-xl);
    color: var(--text-muted);
    cursor: pointer;
    transition: color var(--transition-fast);
    padding: var(--spacing-1);
}

.modal-close:hover {
    color: var(--text-primary);
}

.modal-body {
    padding: var(--spacing-6);
    max-height: 60vh;
    overflow-y: auto;
}

/* ✅ 通知样式 */
.notifications-container {
    position: fixed;
    top: var(--spacing-4);
    right: var(--spacing-4);
    z-index: var(--z-tooltip);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-2);
    max-width: 400px;
}

.notification {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: var(--spacing-4);
    box-shadow: var(--shadow-lg);
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-3);
    animation: slideInRight var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.notification::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: var(--info-color);
}

.notification.success::before {
    background: var(--success-color);
}

.notification.warning::before {
    background: var(--warning-color);
}

.notification.error::before {
    background: var(--error-color);
}

.notification-icon {
    font-size: var(--font-size-lg);
    line-height: 1;
    flex-shrink: 0;
}

.notification-content {
    flex: 1;
    min-width: 0;
}

.notification-title {
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-1);
}

.notification-message {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    line-height: 1.5;
}

.notification-close {
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    font-size: var(--font-size-lg);
    line-height: 1;
    padding: 0;
    flex-shrink: 0;
}

.notification-close:hover {
    color: var(--text-primary);
}

/* ✅ 工具提示样式 */
.tooltip {
    position: absolute;
    z-index: var(--z-tooltip);
    background: var(--text-primary);
    color: var(--text-inverse);
    padding: var(--spacing-2) var(--spacing-3);
    border-radius: var(--border-radius);
    font-size: var(--font-size-xs);
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity var(--transition-fast);
}

.tooltip.show {
    opacity: 1;
}

.tooltip-arrow {
    position: absolute;
    width: 0;
    height: 0;
    border: 4px solid transparent;
}

.tooltip-arrow.top {
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border-top-color: var(--text-primary);
}

.tooltip-arrow.bottom {
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    border-bottom-color: var(--text-primary);
}

/* ✅ 徽章样式 */
.badge {
    display: inline-flex;
    align-items: center;
    padding: var(--spacing-1) var(--spacing-2);
    font-size: var(--font-size-xs);
    font-weight: 500;
    border-radius: var(--border-radius-full);
    background: var(--bg-secondary);
    color: var(--text-secondary);
}

.badge.primary {
    background: var(--primary-light);
    color: var(--primary-color);
}

.badge.success {
    background: var(--success-light);
    color: var(--success-color);
}

.badge.warning {
    background: var(--warning-light);
    color: var(--warning-color);
}

.badge.error {
    background: var(--error-light);
    color: var(--error-color);
}

/* ✅ 进度条样式 */
.progress {
    width: 100%;
    height: 8px;
    background: var(--bg-secondary);
    border-radius: var(--border-radius-full);
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    border-radius: var(--border-radius-full);
    transition: width var(--transition-normal);
}

.progress-bar.animated {
    background-size: 200% 100%;
    animation: progressStripe 2s linear infinite;
}

/* ✅ 分隔符样式 */
.divider {
    height: 1px;
    background: var(--border-color);
    margin: var(--spacing-4) 0;
}

.divider.vertical {
    width: 1px;
    height: auto;
    margin: 0 var(--spacing-4);
}

/* ✅ 加载状态样式 */
.loading {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-2);
    color: var(--text-muted);
    font-size: var(--font-size-sm);
}

.loading-dot {
    width: 4px;
    height: 4px;
    border-radius: var(--border-radius-full);
    background: var(--primary-color);
    animation: loadingDot 1.4s infinite ease-in-out;
}

.loading-dot:nth-child(1) {
    animation-delay: -0.32s;
}

.loading-dot:nth-child(2) {
    animation-delay: -0.16s;
}

/* ✅ 动画定义 */
@keyframes slideInUp {
    from {
        transform: translateY(20px);
        opacity: 0;
    }

    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes progressStripe {
    0% {
        background-position: 200% 0;
    }

    100% {
        background-position: -200% 0;
    }
}

@keyframes loadingDot {

    0%,
    80%,
    100% {
        transform: scale(0);
    }

    40% {
        transform: scale(1);
    }
}

/* ✅ 基准线图例样式 */
.baseline-legend {
    opacity: 0.7;
}

.baseline-color {
    width: 16px;
    height: 2px;
    border-radius: 0;
    background: repeating-linear-gradient(to right,
            transparent,
            transparent 2px,
            rgba(128, 128, 128, 0.3) 2px,
            rgba(128, 128, 128, 0.3) 4px);
}

.baseline-label {
    font-size: 0.9em;
    color: rgba(128, 128, 128, 0.7);
    font-style: italic;
}