{"compilerOptions": {"target": "ES2022", "module": "CommonJS", "downlevelIteration": true, "lib": ["ES2022", "dom"], "types": ["node"], "moduleResolution": "node", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "strict": true, "skipLibCheck": true, "declaration": true, "outDir": "./dist", "rootDir": "./src", "experimentalDecorators": true, "emitDecoratorMetadata": true, "resolveJsonModule": true, "sourceMap": true, "removeComments": false, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noImplicitOverride": true, "exactOptionalPropertyTypes": true, "baseUrl": "./", "paths": {"@/*": ["src/*"], "@business/*": ["src/business/*"], "@services/*": ["src/services/*"], "@adapters/*": ["src/adapters/*"], "@infrastructure/*": ["src/infrastructure/*"], "@types/*": ["src/types/*"], "@utils/*": ["src/utils/*"], "@api/*": ["src/api/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "test", "**/*.test.ts", "**/*.spec.ts"]}