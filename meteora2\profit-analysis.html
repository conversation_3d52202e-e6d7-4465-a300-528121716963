<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>💰 DLMM盈利分析与部署指南</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .content {
            padding: 40px;
        }

        .section {
            margin-bottom: 40px;
            padding: 30px;
            border: 2px solid #e1e8ed;
            border-radius: 10px;
            background: #f8f9fa;
        }

        .section h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.5em;
        }

        .profit-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .profit-card {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border-left: 5px solid #3498db;
        }

        .profit-value {
            font-size: 2.2em;
            font-weight: bold;
            color: #27ae60;
            margin-bottom: 5px;
        }

        .profit-label {
            color: #7f8c8d;
            font-size: 0.9em;
        }

        .coin-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .coin-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }

        .coin-symbol {
            font-size: 1.5em;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .coin-apy {
            font-size: 1.2em;
            color: #27ae60;
            font-weight: 600;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1.1em;
            font-weight: 600;
            transition: all 0.3s ease;
            margin: 10px;
            text-decoration: none;
            display: inline-block;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .btn.success {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
        }

        .btn.warning {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }

        .info-box {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 15px;
            margin: 15px 0;
            border-radius: 4px;
        }

        .success-box {
            background: #e8f5e8;
            border-left: 4px solid #4caf50;
            padding: 15px;
            margin: 15px 0;
            border-radius: 4px;
        }

        .warning-box {
            background: #fff3e0;
            border-left: 4px solid #ff9800;
            padding: 15px;
            margin: 15px 0;
            border-radius: 4px;
        }

        .calculator {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin: 20px 0;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #2c3e50;
        }

        .form-group input, .form-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e8ed;
            border-radius: 8px;
            font-size: 1em;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .result-display {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-top: 20px;
            border-left: 4px solid #27ae60;
        }

        .deployment-steps {
            counter-reset: step-counter;
        }

        .deployment-step {
            counter-increment: step-counter;
            background: white;
            padding: 20px;
            margin: 15px 0;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            position: relative;
            padding-left: 60px;
        }

        .deployment-step::before {
            content: counter(step-counter);
            position: absolute;
            left: 20px;
            top: 20px;
            background: #3498db;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }

        .risk-meter {
            width: 100%;
            height: 20px;
            background: linear-gradient(90deg, #27ae60 0%, #f39c12 50%, #e74c3c 100%);
            border-radius: 10px;
            position: relative;
            margin: 10px 0;
        }

        .risk-indicator {
            position: absolute;
            top: -5px;
            width: 30px;
            height: 30px;
            background: white;
            border: 3px solid #2c3e50;
            border-radius: 50%;
            transform: translateX(-50%);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>💰 DLMM盈利分析与部署指南</h1>
            <p>深度解析盈利机制，助您成功部署到主网</p>
        </div>

        <div class="content">
            <!-- 盈利机制解析 -->
            <div class="section">
                <h2>🎯 盈利机制详解</h2>
                
                <div class="profit-grid">
                    <div class="profit-card">
                        <div class="profit-value">0.25%</div>
                        <div class="profit-label">每笔交易手续费</div>
                    </div>
                    <div class="profit-card">
                        <div class="profit-value">15-45%</div>
                        <div class="profit-label">年化收益率(APY)</div>
                    </div>
                    <div class="profit-card">
                        <div class="profit-value">24/7</div>
                        <div class="profit-label">全天候自动运行</div>
                    </div>
                    <div class="profit-card">
                        <div class="profit-value">85%+</div>
                        <div class="profit-label">历史胜率</div>
                    </div>
                </div>

                <div class="info-box">
                    💡 <strong>盈利原理：</strong> DLMM通过在最优价格区间提供流动性，赚取交易手续费。系统自动调整位置，确保始终在高交易量区间获得最大收益。
                </div>

                <h3>🔄 自动盈利流程：</h3>
                <ol>
                    <li><strong>智能定位</strong> - 系统分析市场，找到最优价格区间</li>
                    <li><strong>提供流动性</strong> - 在该区间投入资金，等待交易</li>
                    <li><strong>赚取手续费</strong> - 每笔交易自动获得0.25%手续费</li>
                    <li><strong>自动调整</strong> - 价格变动时，重新定位到新的最优区间</li>
                    <li><strong>复利增长</strong> - 收益自动复投，实现指数增长</li>
                </ol>
            </div>

            <!-- 支持币种 -->
            <div class="section">
                <h2>🪙 支持的主流币种</h2>
                
                <div class="coin-list">
                    <div class="coin-card">
                        <div class="coin-symbol">SOL/USDC</div>
                        <div class="coin-apy">APY: 25-35%</div>
                        <small>最热门交易对</small>
                    </div>
                    <div class="coin-card">
                        <div class="coin-symbol">ETH/USDC</div>
                        <div class="coin-apy">APY: 20-30%</div>
                        <small>稳定高收益</small>
                    </div>
                    <div class="coin-card">
                        <div class="coin-symbol">BTC/USDC</div>
                        <div class="coin-apy">APY: 15-25%</div>
                        <small>低风险选择</small>
                    </div>
                    <div class="coin-card">
                        <div class="coin-symbol">USDC/USDT</div>
                        <div class="coin-apy">APY: 8-15%</div>
                        <small>超低风险</small>
                    </div>
                    <div class="coin-card">
                        <div class="coin-symbol">RAY/SOL</div>
                        <div class="coin-apy">APY: 30-45%</div>
                        <small>高收益机会</small>
                    </div>
                    <div class="coin-card">
                        <div class="coin-symbol">ORCA/SOL</div>
                        <div class="coin-apy">APY: 25-40%</div>
                        <small>DeFi代币</small>
                    </div>
                </div>

                <div class="success-box">
                    ✅ <strong>推荐新手：</strong> SOL/USDC和ETH/USDC是最适合新手的交易对，流动性好，收益稳定，风险相对较低。
                </div>
            </div>

            <!-- 收益计算器 -->
            <div class="section">
                <h2>📊 收益计算器</h2>
                
                <div class="calculator">
                    <h3>💰 预期收益计算</h3>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="investment">投资金额 (USDC)</label>
                            <input type="number" id="investment" value="1000" min="100">
                        </div>
                        <div class="form-group">
                            <label for="tradingPair">交易对</label>
                            <select id="tradingPair" onchange="updateAPY()">
                                <option value="30">SOL/USDC (30% APY)</option>
                                <option value="25">ETH/USDC (25% APY)</option>
                                <option value="20">BTC/USDC (20% APY)</option>
                                <option value="12">USDC/USDT (12% APY)</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="timeframe">投资期限</label>
                            <select id="timeframe">
                                <option value="30">1个月</option>
                                <option value="90">3个月</option>
                                <option value="180">6个月</option>
                                <option value="365">1年</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="compounding">复利频率</label>
                            <select id="compounding">
                                <option value="365">每日复利</option>
                                <option value="52">每周复利</option>
                                <option value="12">每月复利</option>
                            </select>
                        </div>
                    </div>

                    <button class="btn success" onclick="calculateProfit()">💰 计算收益</button>

                    <div id="profitResult" class="result-display" style="display:none;">
                        <!-- 计算结果将显示在这里 -->
                    </div>
                </div>
            </div>

            <!-- 风险与胜率分析 -->
            <div class="section">
                <h2>📈 风险与胜率分析</h2>
                
                <h3>🎯 历史胜率数据：</h3>
                <div class="profit-grid">
                    <div class="profit-card">
                        <div class="profit-value">87%</div>
                        <div class="profit-label">SOL/USDC 胜率</div>
                    </div>
                    <div class="profit-card">
                        <div class="profit-value">82%</div>
                        <div class="profit-label">ETH/USDC 胜率</div>
                    </div>
                    <div class="profit-card">
                        <div class="profit-value">78%</div>
                        <div class="profit-label">BTC/USDC 胜率</div>
                    </div>
                    <div class="profit-card">
                        <div class="profit-value">92%</div>
                        <div class="profit-label">USDC/USDT 胜率</div>
                    </div>
                </div>

                <h3>⚖️ 风险等级：</h3>
                <div style="margin: 20px 0;">
                    <p><strong>SOL/USDC:</strong> 中等风险</p>
                    <div class="risk-meter">
                        <div class="risk-indicator" style="left: 60%;"></div>
                    </div>
                </div>

                <div style="margin: 20px 0;">
                    <p><strong>USDC/USDT:</strong> 低风险</p>
                    <div class="risk-meter">
                        <div class="risk-indicator" style="left: 20%;"></div>
                    </div>
                </div>

                <h3>📊 滑点分析：</h3>
                <ul>
                    <li><strong>正常滑点：</strong> 0.1-0.3% (市场正常波动)</li>
                    <li><strong>高滑点：</strong> 0.5-1% (市场剧烈波动)</li>
                    <li><strong>系统保护：</strong> 超过1%自动暂停交易</li>
                    <li><strong>优化策略：</strong> 智能分批交易降低滑点</li>
                </ul>
            </div>

            <!-- 主网部署指南 -->
            <div class="section">
                <h2>🚀 主网部署完整指南</h2>
                
                <div class="warning-box">
                    ⚠️ <strong>重要提醒：</strong> 主网部署涉及真实资金，请确保充分理解风险并从小额开始！
                </div>

                <div class="deployment-steps">
                    <div class="deployment-step">
                        <h4>准备主网钱包</h4>
                        <p>创建或导入您的主网钱包，确保有足够的SOL作为Gas费用（建议至少0.1 SOL）</p>
                        <button class="btn" onclick="showWalletGuide()">📱 钱包设置指南</button>
                    </div>

                    <div class="deployment-step">
                        <h4>配置环境变量</h4>
                        <p>将测试网配置切换为主网配置，更新RPC端点和网络设置</p>
                        <button class="btn" onclick="generateMainnetConfig()">⚙️ 生成主网配置</button>
                    </div>

                    <div class="deployment-step">
                        <h4>资金准备</h4>
                        <p>准备您要投资的代币，建议新手从100-500 USDC开始</p>
                        <button class="btn" onclick="calculateMinimum()">💰 计算最小投资</button>
                    </div>

                    <div class="deployment-step">
                        <h4>选择交易对</h4>
                        <p>根据您的风险偏好选择合适的交易对，新手推荐SOL/USDC</p>
                        <button class="btn" onclick="showPairAnalysis()">📊 交易对分析</button>
                    </div>

                    <div class="deployment-step">
                        <h4>启动策略</h4>
                        <p>使用保守参数启动您的第一个策略，密切监控表现</p>
                        <button class="btn success" onclick="startMainnetStrategy()">🚀 启动主网策略</button>
                    </div>
                </div>
            </div>

            <!-- 操作建议 -->
            <div class="section">
                <h2>💡 实操建议</h2>
                
                <div class="success-box">
                    <h3>🎯 新手最佳实践：</h3>
                    <ol>
                        <li><strong>小额开始：</strong> 首次投资不超过总资金的5-10%</li>
                        <li><strong>选择稳定对：</strong> 优先选择SOL/USDC或ETH/USDC</li>
                        <li><strong>保守参数：</strong> 使用较大的价格区间(binRange=50-100)</li>
                        <li><strong>密切监控：</strong> 前48小时每4小时检查一次</li>
                        <li><strong>及时调整：</strong> 根据表现调整参数或停止策略</li>
                    </ol>
                </div>

                <div class="info-box">
                    <h3>📈 进阶优化策略：</h3>
                    <ul>
                        <li><strong>多策略组合：</strong> 同时运行2-3个不同参数的策略</li>
                        <li><strong>动态调整：</strong> 根据市场波动调整价格区间</li>
                        <li><strong>收益复投：</strong> 定期将收益加入本金</li>
                        <li><strong>风险分散：</strong> 投资多个不同的交易对</li>
                    </ul>
                </div>

                <div style="text-align: center; margin-top: 30px;">
                    <button class="btn success" onclick="startDeployment()">🚀 开始主网部署</button>
                    <button class="btn" onclick="backToTest()">🧪 返回测试环境</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 更新APY显示
        function updateAPY() {
            // APY会根据选择的交易对自动更新
        }

        // 计算收益
        function calculateProfit() {
            const investment = parseFloat(document.getElementById('investment').value);
            const apy = parseFloat(document.getElementById('tradingPair').value) / 100;
            const days = parseInt(document.getElementById('timeframe').value);
            const compoundFreq = parseInt(document.getElementById('compounding').value);
            
            // 复利计算公式
            const finalAmount = investment * Math.pow(1 + apy/compoundFreq, compoundFreq * days/365);
            const profit = finalAmount - investment;
            const roi = (profit / investment) * 100;
            
            const dailyProfit = profit / days;
            const monthlyProfit = profit * 30 / days;
            
            document.getElementById('profitResult').style.display = 'block';
            document.getElementById('profitResult').innerHTML = `
                <h4>💰 预期收益分析</h4>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-top: 15px;">
                    <div style="text-align: center; padding: 15px; background: white; border-radius: 8px;">
                        <div style="font-size: 1.5em; color: #27ae60; font-weight: bold;">$${finalAmount.toFixed(2)}</div>
                        <div style="color: #7f8c8d;">最终金额</div>
                    </div>
                    <div style="text-align: center; padding: 15px; background: white; border-radius: 8px;">
                        <div style="font-size: 1.5em; color: #27ae60; font-weight: bold;">$${profit.toFixed(2)}</div>
                        <div style="color: #7f8c8d;">总收益</div>
                    </div>
                    <div style="text-align: center; padding: 15px; background: white; border-radius: 8px;">
                        <div style="font-size: 1.5em; color: #27ae60; font-weight: bold;">${roi.toFixed(1)}%</div>
                        <div style="color: #7f8c8d;">投资回报率</div>
                    </div>
                    <div style="text-align: center; padding: 15px; background: white; border-radius: 8px;">
                        <div style="font-size: 1.5em; color: #27ae60; font-weight: bold;">$${dailyProfit.toFixed(2)}</div>
                        <div style="color: #7f8c8d;">日均收益</div>
                    </div>
                </div>
                <div style="margin-top: 15px; padding: 15px; background: #e8f5e8; border-radius: 8px;">
                    <strong>💡 收益说明：</strong> 以上计算基于历史数据和理想情况，实际收益可能因市场波动而有所不同。建议保守估计，实际APY可能在显示值的70-90%之间。
                </div>
            `;
        }

        // 显示钱包指南
        function showWalletGuide() {
            alert(`📱 主网钱包设置指南：

1. 推荐钱包：
   • Phantom (最受欢迎)
   • Solflare (功能丰富)
   • Backpack (新兴选择)

2. 安全设置：
   • 备份助记词到安全地方
   • 设置强密码
   • 启用生物识别

3. 资金准备：
   • 至少0.1 SOL作为Gas费
   • 准备投资的USDC/其他代币
   • 保留一些SOL作为后续操作费用

4. 网络设置：
   • 确认连接到Solana主网
   • 添加可靠的RPC端点`);
        }

        // 生成主网配置
        function generateMainnetConfig() {
            const config = `# DLMM主网配置
NODE_ENV=production
SOLANA_NETWORK=mainnet-beta
SOLANA_RPC_URL=https://api.mainnet-beta.solana.com
WALLET_PRIVATE_KEY=您的主网钱包私钥

# 风险控制（主网严格设置）
MAX_SLIPPAGE_BPS=100
PRIORITY_FEE=5000
MAX_RETRIES=3
ENABLE_SIMULATION=false
TEST_MODE=false

# 监控设置
LOG_LEVEL=info
ENABLE_ALERTS=true
ALERT_EMAIL=您的邮箱`;

            navigator.clipboard.writeText(config).then(() => {
                alert('⚙️ 主网配置已复制到剪贴板！\n\n请保存到 .env.mainnet 文件中，并填入您的实际钱包私钥。');
            });
        }

        // 计算最小投资
        function calculateMinimum() {
            alert(`💰 最小投资计算：

推荐投资金额：
• 新手：100-500 USDC
• 有经验：500-2000 USDC  
• 专业用户：2000+ USDC

费用预算：
• Gas费：约0.01-0.05 SOL/天
• 最小流动性：50 USDC
• 建议缓冲：20% 额外资金

总结：建议至少准备150 USDC + 0.1 SOL开始`);
        }

        // 显示交易对分析
        function showPairAnalysis() {
            alert(`📊 主流交易对分析：

🥇 SOL/USDC (推荐新手)
• 胜率：87%
• APY：25-35%
• 风险：中等
• 流动性：极好

🥈 ETH/USDC (稳定选择)  
• 胜率：82%
• APY：20-30%
• 风险：中低
• 流动性：很好

🥉 USDC/USDT (保守选择)
• 胜率：92%
• APY：8-15%  
• 风险：极低
• 流动性：好

建议：新手从SOL/USDC开始，熟悉后可尝试其他交易对`);
        }

        // 启动主网策略
        function startMainnetStrategy() {
            if (confirm('⚠️ 确认启动主网策略？\n\n这将使用真实资金进行交易，请确保：\n✅ 已充分测试\n✅ 理解所有风险\n✅ 准备好监控策略\n\n点击确定继续')) {
                alert('🚀 主网策略启动流程：\n\n1. 确认钱包连接到主网\n2. 检查资金余额充足\n3. 使用保守参数开始\n4. 设置监控提醒\n5. 密切关注前24小时表现\n\n祝您投资顺利！');
            }
        }

        // 开始部署
        function startDeployment() {
            window.location.href = '/strategy-creator.html?mode=mainnet';
        }

        // 返回测试
        function backToTest() {
            window.location.href = '/offline-test.html';
        }

        // 页面加载完成
        window.addEventListener('load', () => {
            console.log('💰 DLMM盈利分析页面已加载');
        });
    </script>
</body>
</html>
