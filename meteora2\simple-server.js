// 最简单的HTTP服务器
const http = require('http');
const fs = require('fs');
const path = require('path');

const PORT = 7000;

// MIME类型映射
const mimeTypes = {
    '.html': 'text/html',
    '.js': 'text/javascript',
    '.css': 'text/css',
    '.json': 'application/json',
    '.png': 'image/png',
    '.jpg': 'image/jpeg',
    '.gif': 'image/gif',
    '.ico': 'image/x-icon'
};

// 创建服务器
const server = http.createServer((req, res) => {
    console.log(`${new Date().toLocaleTimeString()} ${req.method} ${req.url}`);
    
    // 设置CORS头
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
    
    if (req.method === 'OPTIONS') {
        res.writeHead(200);
        res.end();
        return;
    }
    
    // API路由
    if (req.url.startsWith('/api/')) {
        handleAPI(req, res);
        return;
    }
    
    // 静态文件服务
    let filePath = req.url === '/' ? '/standalone-test.html' : req.url;
    filePath = path.join(__dirname, filePath);
    
    // 安全检查
    if (!filePath.startsWith(__dirname)) {
        res.writeHead(403);
        res.end('Forbidden');
        return;
    }
    
    fs.readFile(filePath, (err, data) => {
        if (err) {
            res.writeHead(404);
            res.end('File not found');
            return;
        }
        
        const ext = path.extname(filePath);
        const contentType = mimeTypes[ext] || 'text/plain';
        
        res.writeHead(200, { 'Content-Type': contentType });
        res.end(data);
    });
});

// API处理函数
function handleAPI(req, res) {
    const url = req.url;
    
    res.setHeader('Content-Type', 'application/json');
    
    if (url === '/api/health') {
        res.writeHead(200);
        res.end(JSON.stringify({
            status: 'healthy',
            timestamp: new Date().toISOString(),
            uptime: process.uptime() * 1000,
            message: '🎉 简单服务器运行正常',
            services: {
                api: 'healthy',
                server: 'simple-http'
            },
            memory: {
                used: Math.round(process.memoryUsage().heapUsed / 1024 / 1024) + 'MB'
            },
            version: '1.0.0-simple'
        }, null, 2));
        
    } else if (url === '/api/metrics') {
        res.writeHead(200);
        res.end(JSON.stringify({
            status: 'ok',
            timestamp: new Date().toISOString(),
            uptime: {
                ms: process.uptime() * 1000,
                seconds: Math.floor(process.uptime()),
                formatted: formatUptime(process.uptime() * 1000)
            },
            memory: {
                heapUsed: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
                heapTotal: Math.round(process.memoryUsage().heapTotal / 1024 / 1024)
            },
            system: {
                platform: process.platform,
                nodeVersion: process.version,
                pid: process.pid
            }
        }, null, 2));
        
    } else if (url === '/api/strategy') {
        res.writeHead(200);
        res.end(JSON.stringify({
            success: true,
            data: [],
            message: '策略列表（简单模式）'
        }, null, 2));
        
    } else {
        res.writeHead(404);
        res.end(JSON.stringify({
            success: false,
            error: 'API endpoint not found'
        }));
    }
}

function formatUptime(ms) {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) {
        return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
    } else if (minutes > 0) {
        return `${minutes}m ${seconds % 60}s`;
    } else {
        return `${seconds}s`;
    }
}

// 启动服务器
server.listen(PORT, () => {
    console.log('🚀 简单HTTP服务器启动成功！');
    console.log(`🌐 服务器地址: http://localhost:${PORT}`);
    console.log(`❤️ 健康检查: http://localhost:${PORT}/api/health`);
    console.log(`📊 系统指标: http://localhost:${PORT}/api/metrics`);
    console.log(`🧪 测试页面: http://localhost:${PORT}/standalone-test.html`);
    console.log('');
    console.log('💡 这是一个纯Node.js HTTP服务器，无需额外依赖');
    console.log('🎯 现在可以在浏览器中测试了！');
});

// 错误处理
server.on('error', (err) => {
    if (err.code === 'EADDRINUSE') {
        console.log(`❌ 端口 ${PORT} 已被占用`);
        console.log('💡 请关闭其他使用该端口的程序，或重启电脑');
    } else {
        console.log('❌ 服务器错误:', err);
    }
});

// 优雅关闭
process.on('SIGINT', () => {
    console.log('\n🛑 正在关闭服务器...');
    server.close(() => {
        console.log('✅ 服务器已关闭');
        process.exit(0);
    });
});

console.log('⏳ 正在启动服务器...');
