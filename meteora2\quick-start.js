/**
 * 🚀 快速启动脚本 - 绕过npm问题
 * 直接启动API服务器用于测试
 */

const express = require('express');
const cors = require('cors');
const path = require('path');

// 创建简单的API服务器
const app = express();
const PORT = 7000;

// 中间件
app.use(cors());
app.use(express.json());

// 静态文件服务
app.use('/test', express.static(path.join(__dirname, 'web/public/test')));
app.use('/js', express.static(path.join(__dirname, 'web/public/js')));
app.use('/css', express.static(path.join(__dirname, 'web/public/css')));

// 基础健康检查
app.get('/api/health', (req, res) => {
    res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime() * 1000,
        message: '快速启动模式 - API服务器运行正常',
        services: {
            api: 'healthy',
            database: 'mock',
            jupiter: 'mock'
        },
        stats: {
            totalRequests: 0,
            errorRequests: 0,
            successRate: '100%'
        },
        memory: {
            used: Math.round(process.memoryUsage().heapUsed / 1024 / 1024) + 'MB',
            total: Math.round(process.memoryUsage().heapTotal / 1024 / 1024) + 'MB'
        },
        version: '1.0.0-quick'
    });
});

// 指标端点
app.get('/api/metrics', (req, res) => {
    const uptime = process.uptime() * 1000;
    const memoryUsage = process.memoryUsage();
    
    res.json({
        status: 'ok',
        timestamp: new Date().toISOString(),
        uptime: {
            ms: uptime,
            seconds: Math.floor(uptime / 1000),
            formatted: formatUptime(uptime)
        },
        requests: {
            total: 0,
            successful: 0,
            failed: 0,
            successRate: 100
        },
        memory: {
            heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024),
            heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024),
            external: Math.round(memoryUsage.external / 1024 / 1024),
            rss: Math.round(memoryUsage.rss / 1024 / 1024)
        },
        system: {
            platform: process.platform,
            arch: process.arch,
            nodeVersion: process.version,
            pid: process.pid
        },
        services: {
            jupiter: { status: 'mock', version: 'v7' },
            meteora: { status: 'mock' },
            solana: { status: 'mock', network: 'mainnet-beta' }
        }
    });
});

// 模拟策略API
app.get('/api/strategy', (req, res) => {
    res.json({
        success: true,
        data: [],
        message: '快速启动模式 - 策略列表为空'
    });
});

// 模拟Jupiter API
app.get('/api/jupiter/health', (req, res) => {
    res.json({
        success: true,
        data: {
            status: 'healthy',
            message: 'Jupiter服务模拟运行正常',
            version: 'v7-mock'
        }
    });
});

app.get('/api/jupiter/metrics', (req, res) => {
    res.json({
        success: true,
        data: {
            requestCount: 0,
            errorCount: 0,
            successCount: 0,
            successRate: 100,
            avgResponseTime: 0,
            version: 'v7-mock',
            endpoint: 'mock://jupiter-api'
        }
    });
});

// 格式化运行时间
function formatUptime(ms) {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) {
        return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
    } else if (minutes > 0) {
        return `${minutes}m ${seconds % 60}s`;
    } else {
        return `${seconds}s`;
    }
}

// 启动服务器
app.listen(PORT, () => {
    console.log('🚀 快速启动模式 - DLMM API服务器已启动');
    console.log(`🌐 API地址: http://localhost:${PORT}`);
    console.log(`❤️ 健康检查: http://localhost:${PORT}/api/health`);
    console.log(`📊 指标监控: http://localhost:${PORT}/api/metrics`);
    console.log(`🧪 测试页面: http://localhost:${PORT}/test/test-api-integration.html`);
    console.log('');
    console.log('💡 这是一个简化的测试服务器，用于解决npm环境问题');
    console.log('💡 所有API返回模拟数据，但可以正常测试前端功能');
});
