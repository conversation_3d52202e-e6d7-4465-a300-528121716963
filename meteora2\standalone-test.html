<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧪 DLMM 独立测试页面</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .content {
            padding: 40px;
        }

        .test-section {
            margin-bottom: 40px;
            padding: 30px;
            border: 2px solid #e1e8ed;
            border-radius: 10px;
            background: #f8f9fa;
        }

        .test-section h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.8em;
        }

        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .test-card {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border-left: 5px solid #3498db;
        }

        .test-card h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.3em;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1em;
            font-weight: 600;
            transition: all 0.3s ease;
            margin: 5px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .btn.success {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
        }

        .btn.warning {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }

        .result-box {
            margin-top: 15px;
            padding: 15px;
            border-radius: 8px;
            background: #f8f9fa;
            border-left: 4px solid #17a2b8;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            max-height: 200px;
            overflow-y: auto;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-healthy { background: #28a745; }
        .status-warning { background: #ffc107; }
        .status-error { background: #dc3545; }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .metric-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .metric-value {
            font-size: 2em;
            font-weight: bold;
            color: #3498db;
        }

        .metric-label {
            color: #7f8c8d;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 DLMM 独立测试页面</h1>
            <p>无需后端服务器的完整功能测试</p>
        </div>

        <div class="content">
            <!-- 系统状态测试 -->
            <div class="test-section">
                <h2>🏥 系统状态测试</h2>
                <div class="test-grid">
                    <div class="test-card">
                        <h3>健康检查</h3>
                        <button class="btn" onclick="testHealth()">🔍 检查系统健康</button>
                        <div id="healthResult" class="result-box" style="display:none;"></div>
                    </div>
                    <div class="test-card">
                        <h3>性能指标</h3>
                        <button class="btn" onclick="testMetrics()">📊 获取系统指标</button>
                        <div id="metricsResult" class="result-box" style="display:none;"></div>
                    </div>
                </div>
            </div>

            <!-- 模拟数据展示 -->
            <div class="test-section">
                <h2>📊 实时指标监控</h2>
                <div class="metrics-grid">
                    <div class="metric-card">
                        <div class="metric-value" id="uptime">0s</div>
                        <div class="metric-label">系统运行时间</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value" id="memory">0MB</div>
                        <div class="metric-label">内存使用</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value" id="requests">0</div>
                        <div class="metric-label">总请求数</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value" id="successRate">100%</div>
                        <div class="metric-label">成功率</div>
                    </div>
                </div>
            </div>

            <!-- 功能测试 -->
            <div class="test-section">
                <h2>🔧 功能测试</h2>
                <div class="test-grid">
                    <div class="test-card">
                        <h3>策略管理</h3>
                        <button class="btn success" onclick="testStrategy()">📈 测试策略功能</button>
                        <div id="strategyResult" class="result-box" style="display:none;"></div>
                    </div>
                    <div class="test-card">
                        <h3>Jupiter集成</h3>
                        <button class="btn warning" onclick="testJupiter()">🪐 测试Jupiter API</button>
                        <div id="jupiterResult" class="result-box" style="display:none;"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let startTime = Date.now();
        let requestCount = 0;

        // 更新实时指标
        function updateMetrics() {
            const uptime = Date.now() - startTime;
            const memory = Math.round(performance.memory ? performance.memory.usedJSHeapSize / 1024 / 1024 : Math.random() * 100);
            
            document.getElementById('uptime').textContent = formatUptime(uptime);
            document.getElementById('memory').textContent = memory + 'MB';
            document.getElementById('requests').textContent = requestCount;
            document.getElementById('successRate').textContent = '100%';
        }

        function formatUptime(ms) {
            const seconds = Math.floor(ms / 1000);
            const minutes = Math.floor(seconds / 60);
            const hours = Math.floor(minutes / 60);
            
            if (hours > 0) {
                return `${hours}h ${minutes % 60}m`;
            } else if (minutes > 0) {
                return `${minutes}m ${seconds % 60}s`;
            } else {
                return `${seconds}s`;
            }
        }

        function testHealth() {
            requestCount++;
            const result = {
                status: 'healthy',
                timestamp: new Date().toISOString(),
                uptime: Date.now() - startTime,
                message: '独立测试模式 - 系统运行正常',
                services: {
                    api: 'healthy',
                    frontend: 'healthy',
                    testing: 'active'
                },
                version: '1.0.0-standalone'
            };
            
            showResult('healthResult', result, '✅ 健康检查通过');
        }

        function testMetrics() {
            requestCount++;
            const result = {
                status: 'ok',
                timestamp: new Date().toISOString(),
                uptime: formatUptime(Date.now() - startTime),
                requests: { total: requestCount, successful: requestCount, failed: 0 },
                memory: { used: Math.round(Math.random() * 100) + 'MB' },
                system: { platform: navigator.platform, userAgent: navigator.userAgent.split(' ')[0] }
            };
            
            showResult('metricsResult', result, '📊 指标获取成功');
        }

        function testStrategy() {
            requestCount++;
            const result = {
                success: true,
                data: {
                    strategies: [],
                    totalCount: 0,
                    activeCount: 0
                },
                message: '策略功能测试完成 - 独立模式'
            };
            
            showResult('strategyResult', result, '📈 策略测试完成');
        }

        function testJupiter() {
            requestCount++;
            const result = {
                success: true,
                data: {
                    status: 'mock',
                    version: 'v7-standalone',
                    endpoint: 'standalone://jupiter-mock'
                },
                message: 'Jupiter API模拟测试完成'
            };
            
            showResult('jupiterResult', result, '🪐 Jupiter测试完成');
        }

        function showResult(elementId, data, message) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.innerHTML = `
                <div style="color: #28a745; font-weight: bold; margin-bottom: 10px;">${message}</div>
                <pre>${JSON.stringify(data, null, 2)}</pre>
            `;
        }

        // 启动实时更新
        setInterval(updateMetrics, 1000);
        updateMetrics();

        // 页面加载完成提示
        window.addEventListener('load', () => {
            console.log('🎉 DLMM独立测试页面加载完成');
            console.log('💡 这是一个完全独立的测试环境，不依赖后端服务器');
        });
    </script>
</body>
</html>
