/**
 * 🎨 池爬虫监控样式
 * 专门为池爬虫监控功能设计，集成到现有监控系统中
 */



/* 池爬虫监控样式 */
.pool-crawler-monitor {
    width: 100%;
    max-width: none;
    margin: 0;
    padding: 0;
}

/* 监控头部样式 */
.monitor-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding: 20px;
    background: var(--bg-tertiary, #2a2a2a);
    border-radius: 8px;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 20px;
}

.header-left h3 {
    display: flex;
    align-items: center;
    gap: 8px;
    margin: 0;
    color: var(--text-primary, #fff);
    font-size: 18px;
    font-weight: 600;
}

.header-left .icon {
    font-size: 20px;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 24px;
}

/* 连接状态样式 */
.connection-status {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    border-radius: 20px;
    background: var(--bg-primary, #0a0a0a);
    font-size: 14px;
    font-weight: 500;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--text-secondary, #666);
    animation: pulse 2s infinite;
}

.status-dot.connected {
    background: var(--success-color, #10b981);
}

.status-dot.subscribed {
    background: var(--primary-color, #00d4aa);
}

.status-dot.disconnected {
    background: var(--error-color, #ef4444);
}

.status-dot.error {
    background: var(--warning-color, #f59e0b);
}

.status-text {
    color: var(--text-secondary, #999);
    font-size: 13px;
}

/* 统计数据样式 */
.crawler-stats {
    display: flex;
    gap: 20px;
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    min-width: 80px;
}

.stat-value {
    font-size: 24px;
    font-weight: 700;
    color: var(--primary-color, #00d4aa);
    margin-bottom: 4px;
}

.stat-label {
    font-size: 12px;
    color: var(--text-secondary, #999);
    font-weight: 500;
}

/* 监控操作按钮 */
.monitor-actions {
    display: flex;
    gap: 8px;
}

.btn {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 13px;
}

.btn-primary {
    background: var(--primary-color, #00d4aa);
    color: var(--bg-primary, #0a0a0a);
}

.btn-primary:hover {
    background: var(--primary-color-hover, #00b894);
}

.btn-secondary {
    background: var(--bg-tertiary, #2a2a2a);
    color: var(--text-secondary, #999);
    border: 1px solid var(--border-color, #333);
}

.btn-secondary:hover {
    background: var(--bg-quaternary, #3a3a3a);
    color: var(--text-primary, #fff);
}

.btn .icon {
    font-size: 14px;
}

/* 爬虫状态区域 */
.crawler-status-section {
    margin-bottom: 24px;
}

.status-card {
    background: var(--bg-tertiary, #2a2a2a);
    border-radius: 8px;
    padding: 20px;
    border: 1px solid var(--border-color, #333);
}

.status-header {
    margin-bottom: 16px;
}

.status-header h4 {
    margin: 0;
    color: var(--text-primary, #fff);
    font-size: 16px;
    font-weight: 600;
}

.status-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
}

.status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: var(--bg-primary, #0a0a0a);
    border-radius: 6px;
    border: 1px solid var(--border-color, #333);
}

.status-item label {
    color: var(--text-secondary, #999);
    font-size: 14px;
    font-weight: 500;
}

.status-value {
    color: var(--text-primary, #fff);
    font-size: 14px;
    font-weight: 600;
}

.status-value.running {
    color: var(--success-color, #10b981);
}

.status-value.stopped {
    color: var(--error-color, #ef4444);
}

/* 池数据区域 */
.pools-section {
    background: var(--bg-tertiary, #2a2a2a);
    border-radius: 8px;
    border: 1px solid var(--border-color, #333);
    overflow: hidden;
}

.section-tabs {
    display: flex;
    border-bottom: 1px solid var(--border-color, #333);
    background: var(--bg-quaternary, #3a3a3a);
}

.tab-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 14px 20px;
    border: none;
    background: transparent;
    color: var(--text-secondary, #999);
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: all 0.3s ease;
}

.tab-btn:hover {
    color: var(--text-primary, #fff);
    background: rgba(255, 255, 255, 0.05);
}

.tab-btn.active {
    color: var(--primary-color, #00d4aa);
    border-bottom-color: var(--primary-color, #00d4aa);
}

.tab-btn .icon {
    font-size: 16px;
}

.tab-content {
    display: none;
    padding: 20px;
}

.tab-content.active {
    display: block;
}

/* 池表格样式 */
.pools-table-container {
    overflow-x: auto;
    border-radius: 8px;
    background: var(--bg-primary, #0a0a0a);
    border: 1px solid var(--border-color, #333);
}

.pools-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
}

.pools-table th {
    background: var(--bg-quaternary, #3a3a3a);
    color: var(--text-primary, #fff);
    padding: 12px 16px;
    text-align: left;
    font-weight: 600;
    border-bottom: 2px solid var(--border-color, #333);
}

.pools-table td {
    padding: 12px 16px;
    border-bottom: 1px solid var(--border-color, #333);
    color: var(--text-secondary, #999);
}

.pools-table tr:hover {
    background: var(--bg-secondary, #1a1a1a);
}

.pool-address {
    font-family: 'Courier New', monospace;
    font-size: 13px;
    color: var(--primary-color, #00d4aa);
}

.empty-state {
    text-align: center;
    color: var(--text-secondary, #666);
    font-style: italic;
    padding: 40px;
}

/* 过滤器样式 */
.filters-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.filter-section {
    background: var(--bg-primary, #0a0a0a);
    border-radius: 8px;
    padding: 16px;
    border: 1px solid var(--border-color, #333);
}

.filter-section h5 {
    margin: 0 0 12px 0;
    color: var(--text-primary, #fff);
    font-size: 14px;
    font-weight: 600;
}

.filter-item {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-bottom: 12px;
}

.filter-item:last-child {
    margin-bottom: 0;
}

.filter-item label {
    color: var(--text-secondary, #999);
    font-size: 13px;
    font-weight: 500;
}

.filter-item input {
    padding: 8px 12px;
    border: 1px solid var(--border-color, #333);
    border-radius: 6px;
    background: var(--bg-secondary, #1a1a1a);
    color: var(--text-primary, #fff);
    font-size: 14px;
    transition: border-color 0.2s ease;
}

.filter-item input:focus {
    outline: none;
    border-color: var(--primary-color, #00d4aa);
}

.filter-item input::placeholder {
    color: var(--text-secondary, #666);
}

.filter-actions {
    grid-column: 1 / -1;
    display: flex;
    gap: 12px;
    justify-content: center;
    padding-top: 16px;
    border-top: 1px solid var(--border-color, #333);
}

/* 通知样式 */
.notification-area {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
    max-width: 400px;
}

.notification {
    display: flex;
    align-items: flex-start;
    padding: 16px;
    margin-bottom: 12px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    animation: slideIn 0.3s ease;
}

.notification.success {
    background: var(--success-color, #10b981);
    color: white;
}

.notification.error {
    background: var(--error-color, #ef4444);
    color: white;
}

.notification.warning {
    background: var(--warning-color, #f59e0b);
    color: white;
}

.notification.info {
    background: var(--info-color, #3b82f6);
    color: white;
}

.notification-content {
    flex: 1;
}

.notification-title {
    font-weight: 600;
    font-size: 14px;
    margin-bottom: 4px;
}

.notification-message {
    font-size: 13px;
    opacity: 0.9;
}

.notification-close {
    background: none;
    border: none;
    color: white;
    font-size: 18px;
    cursor: pointer;
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 12px;
    opacity: 0.8;
}

.notification-close:hover {
    opacity: 1;
}

/* 动画 */
@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(0, 212, 170, 0.4);
    }

    70% {
        box-shadow: 0 0 0 10px rgba(0, 212, 170, 0);
    }

    100% {
        box-shadow: 0 0 0 0 rgba(0, 212, 170, 0);
    }
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }

    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .monitor-header {
        flex-direction: column;
        gap: 16px;
        align-items: flex-start;
    }

    .header-right {
        width: 100%;
        justify-content: space-between;
    }

    .crawler-stats {
        flex-wrap: wrap;
        gap: 16px;
    }

    .status-content {
        grid-template-columns: 1fr;
    }

    .filters-container {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .pools-table {
        font-size: 12px;
    }

    .pools-table th,
    .pools-table td {
        padding: 8px 12px;
    }

    .monitor-actions {
        flex-direction: column;
        gap: 8px;
        width: 100%;
    }

    .btn {
        width: 100%;
        justify-content: center;
    }

    .notification-area {
        left: 20px;
        right: 20px;
        max-width: none;
    }
}

/* 深色主题支持 */
@media (prefers-color-scheme: dark) {
    :root {
        --bg-primary: #0a0a0a;
        --bg-secondary: #1a1a1a;
        --bg-tertiary: #2a2a2a;
        --bg-quaternary: #3a3a3a;
        --text-primary: #ffffff;
        --text-secondary: #999999;
        --border-color: #333333;
        --primary-color: #00d4aa;
        --primary-color-hover: #00b894;
        --success-color: #10b981;
        --error-color: #ef4444;
        --warning-color: #f59e0b;
        --info-color: #3b82f6;
    }
}

/* 加载状态 */
.loading-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    color: var(--text-secondary, #999);
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--border-color, #333);
    border-top: 4px solid var(--primary-color, #00d4aa);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

/* 过滤器状态样式 */
.filter-enabled {
    border-left: 4px solid var(--success-color, #10b981);
    background: rgba(16, 185, 129, 0.05);
}

.filter-disabled {
    border-left: 4px solid var(--text-secondary, #999);
    background: rgba(153, 153, 153, 0.05);
}

.filter-partial {
    border-left: 4px solid var(--warning-color, #f59e0b);
    background: rgba(245, 158, 11, 0.05);
}

.filter-status {
    font-size: 12px;
    font-weight: 500;
    padding: 2px 8px;
    border-radius: 12px;
    white-space: nowrap;
    margin-left: 8px;
}

.filter-enabled .filter-status {
    background: rgba(16, 185, 129, 0.2);
    color: var(--success-color, #10b981);
}

.filter-disabled .filter-status {
    background: rgba(153, 153, 153, 0.2);
    color: var(--text-secondary, #999);
}

.filter-partial .filter-status {
    background: rgba(245, 158, 11, 0.2);
    color: var(--warning-color, #f59e0b);
}

/* 禁用状态的输入框样式 */
.filter-disabled input:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    background: rgba(153, 153, 153, 0.1);
}

.filter-disabled input:disabled::placeholder {
    color: var(--text-secondary, #999);
    opacity: 0.7;
}

/* 部分支持的过滤器内部样式 */
.filter-partial .timeframe-group.filter-enabled {
    border-left: 2px solid var(--success-color, #10b981);
    padding-left: 12px;
    margin-left: 8px;
}

.filter-partial .timeframe-group.filter-disabled {
    border-left: 2px solid var(--text-secondary, #999);
    padding-left: 12px;
    margin-left: 8px;
    opacity: 0.6;
}

.filter-partial .timeframe-group.filter-disabled label {
    color: var(--text-secondary, #999);
}

/* 过滤器标题样式增强 */
.filter-section h5 {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    margin-bottom: 12px;
}

/* 启用状态的输入框增强 */
.filter-enabled input:focus {
    border-color: var(--success-color, #10b981);
    box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.1);
}

/* 🔔 声音控件样式 */
.sound-control {
    margin-left: 10px;
    display: flex;
    align-items: center;
}

.sound-toggle {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 12px;
    color: #666;
    user-select: none;
    transition: color 0.2s ease;
}

.sound-toggle:hover {
    color: #333;
}

.sound-toggle input[type="checkbox"] {
    display: none;
}

.sound-icon {
    font-size: 16px;
    margin-right: 4px;
    transition: transform 0.2s ease;
}

.sound-toggle:hover .sound-icon {
    transform: scale(1.1);
}

.sound-toggle input[type="checkbox"]:checked+.sound-icon {
    filter: drop-shadow(0 0 3px rgba(0, 150, 255, 0.5));
}

.sound-label {
    font-weight: 500;
    white-space: nowrap;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .sound-control {
        margin-left: 5px;
    }

    .sound-label {
        display: none;
        /* 在小屏幕上隐藏文字，只显示图标 */
    }

    .sound-icon {
        font-size: 18px;
    }
}