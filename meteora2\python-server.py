#!/usr/bin/env python3
"""
🚀 DLMM Python测试服务器
绕过Node.js环境问题的简单HTTP服务器
"""

import http.server
import socketserver
import json
import os
import time
from urllib.parse import urlparse, parse_qs

PORT = 7000

class DLMMHandler(http.server.SimpleHTTPRequestHandler):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, directory=os.getcwd(), **kwargs)
    
    def do_GET(self):
        parsed_path = urlparse(self.path)
        
        # API路由处理
        if parsed_path.path.startswith('/api/'):
            self.handle_api(parsed_path.path)
            return
        
        # 默认页面重定向
        if parsed_path.path == '/':
            self.path = '/standalone-test.html'
        
        # 静态文件处理
        super().do_GET()
    
    def do_OPTIONS(self):
        self.send_response(200)
        self.send_cors_headers()
        self.end_headers()
    
    def send_cors_headers(self):
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
    
    def handle_api(self, path):
        self.send_response(200)
        self.send_header('Content-Type', 'application/json')
        self.send_cors_headers()
        self.end_headers()
        
        if path == '/api/health':
            response = {
                "status": "healthy",
                "timestamp": time.strftime('%Y-%m-%dT%H:%M:%S.000Z'),
                "uptime": int(time.time() * 1000),
                "message": "🎉 Python服务器运行正常",
                "services": {
                    "api": "healthy",
                    "server": "python-http"
                },
                "version": "1.0.0-python"
            }
        
        elif path == '/api/metrics':
            uptime_seconds = int(time.time())
            response = {
                "status": "ok",
                "timestamp": time.strftime('%Y-%m-%dT%H:%M:%S.000Z'),
                "uptime": {
                    "ms": uptime_seconds * 1000,
                    "seconds": uptime_seconds,
                    "formatted": f"{uptime_seconds}s"
                },
                "system": {
                    "platform": os.name,
                    "pid": os.getpid()
                },
                "server": "python-http"
            }
        
        elif path == '/api/strategy':
            response = {
                "success": True,
                "data": [],
                "message": "策略列表（Python模式）"
            }
        
        else:
            response = {
                "success": False,
                "error": "API endpoint not found"
            }
        
        self.wfile.write(json.dumps(response, indent=2, ensure_ascii=False).encode('utf-8'))

def main():
    try:
        with socketserver.TCPServer(("", PORT), DLMMHandler) as httpd:
            print("🚀 Python HTTP服务器启动成功！")
            print("=" * 40)
            print(f"🌐 服务器地址: http://localhost:{PORT}")
            print(f"❤️ 健康检查: http://localhost:{PORT}/api/health")
            print(f"📊 系统指标: http://localhost:{PORT}/api/metrics")
            print(f"🧪 测试页面: http://localhost:{PORT}/standalone-test.html")
            print("")
            print("💡 这是一个纯Python HTTP服务器")
            print("🎯 现在可以在浏览器中测试了！")
            print("")
            print("按 Ctrl+C 停止服务器")
            
            httpd.serve_forever()
            
    except OSError as e:
        if e.errno == 10048:  # Windows端口占用错误
            print(f"❌ 端口 {PORT} 已被占用")
            print("💡 请关闭其他使用该端口的程序，或重启电脑")
        else:
            print(f"❌ 服务器错误: {e}")
    except KeyboardInterrupt:
        print("\n🛑 服务器已停止")

if __name__ == "__main__":
    main()
