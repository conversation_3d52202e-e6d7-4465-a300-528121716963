// 超简单的HTTP服务器 - 绕过所有问题
const http = require('http');
const fs = require('fs');
const path = require('path');

const PORT = 7000;
console.log('🚀 启动超简单服务器...');

const server = http.createServer((req, res) => {
    // 设置CORS
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type');
    
    if (req.method === 'OPTIONS') {
        res.writeHead(200);
        res.end();
        return;
    }
    
    console.log(`${new Date().toLocaleTimeString()} ${req.method} ${req.url}`);
    
    // API路由
    if (req.url === '/api/health') {
        res.writeHead(200, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({
            status: 'healthy',
            message: '🎉 超简单服务器运行正常',
            timestamp: new Date().toISOString(),
            uptime: process.uptime() * 1000
        }));
        return;
    }
    
    if (req.url === '/api/metrics') {
        res.writeHead(200, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({
            status: 'ok',
            uptime: Math.floor(process.uptime()),
            memory: Math.round(process.memoryUsage().heapUsed / 1024 / 1024) + 'MB',
            timestamp: new Date().toISOString()
        }));
        return;
    }
    
    if (req.url === '/api/strategy' && req.method === 'POST') {
        let body = '';
        req.on('data', chunk => body += chunk);
        req.on('end', () => {
            res.writeHead(200, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({
                success: true,
                data: {
                    id: 'strategy_' + Date.now(),
                    name: '测试策略',
                    status: 'created'
                },
                message: '策略创建成功'
            }));
        });
        return;
    }
    
    // 静态文件
    let filePath = req.url === '/' ? '/strategy-creator.html' : req.url;
    filePath = path.join(__dirname, filePath);
    
    fs.readFile(filePath, (err, data) => {
        if (err) {
            res.writeHead(404);
            res.end('File not found');
            return;
        }
        
        let contentType = 'text/html';
        if (filePath.endsWith('.js')) contentType = 'text/javascript';
        if (filePath.endsWith('.css')) contentType = 'text/css';
        if (filePath.endsWith('.json')) contentType = 'application/json';
        
        res.writeHead(200, { 'Content-Type': contentType });
        res.end(data);
    });
});

server.listen(PORT, () => {
    console.log('');
    console.log('🎉 超简单服务器启动成功！');
    console.log(`🌐 地址: http://localhost:${PORT}`);
    console.log(`🚀 策略创建器: http://localhost:${PORT}/strategy-creator.html`);
    console.log(`❤️ 健康检查: http://localhost:${PORT}/api/health`);
    console.log('');
    console.log('💡 服务器正在运行，按 Ctrl+C 停止');
});

server.on('error', (err) => {
    console.log('❌ 服务器错误:', err.message);
    if (err.code === 'EADDRINUSE') {
        console.log('💡 端口被占用，尝试关闭其他程序或重启电脑');
    }
});

process.on('SIGINT', () => {
    console.log('\n🛑 服务器已停止');
    process.exit(0);
});
