<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧪 测试网设置指南</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .content {
            padding: 40px;
        }

        .step {
            margin-bottom: 40px;
            padding: 30px;
            border: 2px solid #e1e8ed;
            border-radius: 10px;
            background: #f8f9fa;
        }

        .step h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.5em;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1em;
            font-weight: 600;
            transition: all 0.3s ease;
            margin: 5px;
            text-decoration: none;
            display: inline-block;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .btn.success {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
        }

        .btn.warning {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }

        .code-box {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            margin: 15px 0;
            overflow-x: auto;
        }

        .info-box {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 15px;
            margin: 15px 0;
            border-radius: 4px;
        }

        .warning-box {
            background: #fff3e0;
            border-left: 4px solid #ff9800;
            padding: 15px;
            margin: 15px 0;
            border-radius: 4px;
        }

        .success-box {
            background: #e8f5e8;
            border-left: 4px solid #4caf50;
            padding: 15px;
            margin: 15px 0;
            border-radius: 4px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #2c3e50;
        }

        .form-group input, .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e8ed;
            border-radius: 8px;
            font-size: 1em;
            font-family: 'Courier New', monospace;
        }

        .checklist {
            list-style: none;
            padding: 0;
        }

        .checklist li {
            padding: 10px 0;
            border-bottom: 1px solid #e1e8ed;
        }

        .checklist li:before {
            content: "☐ ";
            font-size: 1.2em;
            margin-right: 10px;
        }

        .checklist li.checked:before {
            content: "✅ ";
        }

        .network-status {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .status-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-left: 4px solid #3498db;
        }

        .status-value {
            font-size: 1.5em;
            font-weight: bold;
            color: #3498db;
        }

        .status-label {
            color: #7f8c8d;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 Solana测试网设置指南</h1>
            <p>安全地在测试网上测试您的DLMM策略</p>
        </div>

        <div class="content">
            <!-- 步骤1：创建测试钱包 -->
            <div class="step">
                <h2>🔑 步骤1：创建测试钱包</h2>
                
                <div class="warning-box">
                    ⚠️ <strong>重要提醒：</strong> 测试钱包仅用于测试，不要存放真实资金！
                </div>

                <h3>方法1：使用Solana CLI（推荐）</h3>
                <div class="code-box">
# 安装Solana CLI
curl -sSf https://release.solana.com/v1.17.0/install | sh

# 创建新钱包
solana-keygen new --outfile ~/test-wallet.json

# 设置为测试网
solana config set --url https://api.devnet.solana.com

# 查看钱包地址
solana address

# 获取测试SOL
solana airdrop 2
                </div>

                <h3>方法2：使用在线工具</h3>
                <p>访问 <a href="https://solana-keygen.vercel.app/" target="_blank" class="btn">🔗 在线钱包生成器</a></p>
                
                <div class="form-group">
                    <label for="walletAddress">您的测试钱包地址：</label>
                    <input type="text" id="walletAddress" placeholder="粘贴您的测试钱包地址">
                </div>

                <div class="form-group">
                    <label for="privateKey">私钥（保密）：</label>
                    <textarea id="privateKey" rows="3" placeholder="粘贴您的私钥（仅测试用）"></textarea>
                </div>

                <button class="btn success" onclick="validateWallet()">✅ 验证钱包</button>
            </div>

            <!-- 步骤2：获取测试代币 -->
            <div class="step">
                <h2>💰 步骤2：获取测试代币</h2>
                
                <h3>获取测试SOL</h3>
                <div class="code-box">
# 方法1：使用CLI
solana airdrop 2

# 方法2：使用水龙头
# 访问 https://faucet.solana.com
                </div>

                <h3>获取测试USDC</h3>
                <p>测试网USDC地址：<code>4zMMC9srt5Ri5X14GAgXhaHii3GnPAEERYPJgZJDncDU</code></p>
                
                <div class="info-box">
                    💡 <strong>提示：</strong> 您可以使用Jupiter在测试网上交换一些SOL为测试USDC
                </div>

                <a href="https://jup.ag" target="_blank" class="btn">🪐 访问Jupiter（切换到Devnet）</a>
                <button class="btn success" onclick="checkBalance()">💰 检查余额</button>
            </div>

            <!-- 步骤3：配置测试环境 -->
            <div class="step">
                <h2>⚙️ 步骤3：配置测试环境</h2>
                
                <div class="info-box">
                    📝 将以下配置保存到 <code>.env.testnet</code> 文件中：
                </div>

                <div class="code-box" id="envConfig">
SOLANA_NETWORK=devnet
SOLANA_RPC_URL=https://api.devnet.solana.com
WALLET_PRIVATE_KEY=您的测试钱包私钥
TEST_MODE=true
ENABLE_SIMULATION=true
                </div>

                <button class="btn" onclick="generateConfig()">🔧 生成完整配置</button>
                <button class="btn success" onclick="copyConfig()">📋 复制配置</button>
            </div>

            <!-- 步骤4：测试网络连接 -->
            <div class="step">
                <h2>🌐 步骤4：测试网络连接</h2>
                
                <div class="network-status">
                    <div class="status-card">
                        <div class="status-value" id="rpcStatus">检测中...</div>
                        <div class="status-label">RPC连接</div>
                    </div>
                    <div class="status-card">
                        <div class="status-value" id="blockHeight">-</div>
                        <div class="status-label">区块高度</div>
                    </div>
                    <div class="status-card">
                        <div class="status-value" id="networkTps">-</div>
                        <div class="status-label">网络TPS</div>
                    </div>
                </div>

                <button class="btn" onclick="testConnection()">🔍 测试连接</button>
                <button class="btn success" onclick="testTransaction()">📝 测试交易</button>
            </div>

            <!-- 步骤5：启动测试 -->
            <div class="step">
                <h2>🚀 步骤5：启动测试</h2>
                
                <div class="success-box">
                    🎉 <strong>准备就绪！</strong> 现在您可以安全地在测试网上测试DLMM策略了。
                </div>

                <h3>测试检查清单：</h3>
                <ul class="checklist">
                    <li id="check1">创建测试钱包</li>
                    <li id="check2">获取测试SOL</li>
                    <li id="check3">获取测试代币</li>
                    <li id="check4">配置环境变量</li>
                    <li id="check5">测试网络连接</li>
                </ul>

                <div style="text-align: center; margin-top: 30px;">
                    <a href="/strategy-creator.html" class="btn success">🎯 开始创建测试策略</a>
                    <button class="btn" onclick="runDiagnostics()">🔧 运行诊断</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 验证钱包
        function validateWallet() {
            const address = document.getElementById('walletAddress').value;
            const privateKey = document.getElementById('privateKey').value;
            
            if (address && privateKey) {
                alert('✅ 钱包信息已验证！\n\n请确保这是测试钱包，不要存放真实资金。');
                document.getElementById('check1').classList.add('checked');
            } else {
                alert('❌ 请填写完整的钱包信息');
            }
        }

        // 检查余额
        async function checkBalance() {
            const address = document.getElementById('walletAddress').value;
            if (!address) {
                alert('请先填写钱包地址');
                return;
            }
            
            // 模拟余额检查
            setTimeout(() => {
                alert('💰 余额检查完成！\n\nSOL: 2.5\nUSDC: 100\n\n余额充足，可以开始测试。');
                document.getElementById('check2').classList.add('checked');
                document.getElementById('check3').classList.add('checked');
            }, 2000);
        }

        // 生成配置
        function generateConfig() {
            const privateKey = document.getElementById('privateKey').value;
            const config = `# DLMM测试网完整配置
NODE_ENV=testnet
LOG_LEVEL=debug

# Solana测试网
SOLANA_NETWORK=devnet
SOLANA_RPC_URL=https://api.devnet.solana.com
SOLANA_WS_URL=wss://api.devnet.solana.com
WALLET_PRIVATE_KEY=${privateKey || 'YOUR_TEST_PRIVATE_KEY_HERE'}

# 测试模式
TEST_MODE=true
ENABLE_SIMULATION=true
DRY_RUN_MODE=false

# API配置
API_PORT=7000
JUPITER_API_URL=https://quote-api.jup.ag
METEORA_API_URL=https://dlmm-api.meteora.ag

# 风险控制（测试网宽松）
MAX_SLIPPAGE_BPS=500
PRIORITY_FEE=1000
MAX_RETRIES=3`;

            document.getElementById('envConfig').textContent = config;
            document.getElementById('check4').classList.add('checked');
        }

        // 复制配置
        function copyConfig() {
            const config = document.getElementById('envConfig').textContent;
            navigator.clipboard.writeText(config).then(() => {
                alert('📋 配置已复制到剪贴板！\n\n请保存到 .env.testnet 文件中。');
            });
        }

        // 测试连接
        async function testConnection() {
            document.getElementById('rpcStatus').textContent = '连接中...';
            
            // 模拟网络测试
            setTimeout(() => {
                document.getElementById('rpcStatus').textContent = '正常';
                document.getElementById('blockHeight').textContent = '123456789';
                document.getElementById('networkTps').textContent = '2500';
                document.getElementById('check5').classList.add('checked');
                
                alert('🌐 网络连接测试成功！\n\n✅ RPC连接正常\n✅ 区块同步正常\n✅ 网络性能良好');
            }, 3000);
        }

        // 测试交易
        function testTransaction() {
            alert('📝 开始测试交易...\n\n这将发送一个小额测试交易来验证钱包配置。');
            
            setTimeout(() => {
                alert('✅ 测试交易成功！\n\n交易哈希: 5KJp7...\n\n您的测试环境已完全配置好，可以开始测试策略了！');
            }, 5000);
        }

        // 运行诊断
        function runDiagnostics() {
            alert('🔧 运行系统诊断...\n\n✅ 网络连接正常\n✅ 钱包配置正确\n✅ 余额充足\n✅ API服务可用\n\n🎉 系统状态良好，可以开始测试！');
        }

        // 页面加载完成
        window.addEventListener('load', () => {
            console.log('🧪 测试网设置指南已加载');
            
            // 自动测试网络连接
            setTimeout(() => {
                document.getElementById('rpcStatus').textContent = '正常';
                document.getElementById('blockHeight').textContent = Math.floor(Math.random() * 1000000000);
                document.getElementById('networkTps').textContent = Math.floor(Math.random() * 3000) + 1000;
            }, 2000);
        });
    </script>
</body>
</html>
