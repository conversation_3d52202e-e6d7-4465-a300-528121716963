// 🧪 DLMM测试网专用服务器
const http = require('http');
const fs = require('fs');
const path = require('path');

const PORT = 7001; // 使用不同端口避免冲突
console.log('🧪 启动DLMM测试网服务器...');

// 测试网配置
const TESTNET_CONFIG = {
    network: 'devnet',
    rpcUrl: 'https://api.devnet.solana.com',
    wsUrl: 'wss://api.devnet.solana.com',
    jupiterUrl: 'https://quote-api.jup.ag',
    meteoraUrl: 'https://dlmm-api.meteora.ag',
    testMode: true,
    enableSimulation: true
};

// 模拟测试钱包数据
let testWallet = {
    address: '',
    balance: {
        SOL: 2.5,
        USDC: 100.0
    },
    connected: false
};

// 模拟策略数据
let testStrategies = [];

const server = http.createServer((req, res) => {
    // 设置CORS
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
    
    if (req.method === 'OPTIONS') {
        res.writeHead(200);
        res.end();
        return;
    }
    
    console.log(`🧪 ${new Date().toLocaleTimeString()} ${req.method} ${req.url}`);
    
    // API路由处理
    if (req.url.startsWith('/api/')) {
        handleTestnetAPI(req, res);
        return;
    }
    
    // 静态文件服务
    let filePath = req.url === '/' ? '/testnet-setup.html' : req.url;
    filePath = path.join(__dirname, filePath);
    
    fs.readFile(filePath, (err, data) => {
        if (err) {
            res.writeHead(404);
            res.end('File not found');
            return;
        }
        
        let contentType = 'text/html';
        if (filePath.endsWith('.js')) contentType = 'text/javascript';
        if (filePath.endsWith('.css')) contentType = 'text/css';
        if (filePath.endsWith('.json')) contentType = 'application/json';
        
        res.writeHead(200, { 'Content-Type': contentType });
        res.end(data);
    });
});

// 测试网API处理
function handleTestnetAPI(req, res) {
    const url = req.url;
    res.setHeader('Content-Type', 'application/json');
    
    // 健康检查
    if (url === '/api/health') {
        res.writeHead(200);
        res.end(JSON.stringify({
            status: 'healthy',
            network: 'devnet',
            timestamp: new Date().toISOString(),
            uptime: process.uptime() * 1000,
            message: '🧪 测试网服务器运行正常',
            config: TESTNET_CONFIG,
            testMode: true
        }));
        return;
    }
    
    // 网络状态
    if (url === '/api/network/status') {
        res.writeHead(200);
        res.end(JSON.stringify({
            success: true,
            data: {
                network: 'devnet',
                rpcUrl: TESTNET_CONFIG.rpcUrl,
                blockHeight: Math.floor(Math.random() * **********) + 200000000,
                tps: Math.floor(Math.random() * 3000) + 1000,
                epochInfo: {
                    epoch: 500 + Math.floor(Math.random() * 100),
                    slotIndex: Math.floor(Math.random() * 432000),
                    slotsInEpoch: 432000
                },
                health: 'ok'
            }
        }));
        return;
    }
    
    // 钱包连接
    if (url === '/api/wallet/connect' && req.method === 'POST') {
        let body = '';
        req.on('data', chunk => body += chunk);
        req.on('end', () => {
            try {
                const data = JSON.parse(body);
                testWallet.address = data.address || 'TestWallet' + Date.now();
                testWallet.connected = true;
                
                res.writeHead(200);
                res.end(JSON.stringify({
                    success: true,
                    data: {
                        address: testWallet.address,
                        network: 'devnet',
                        balance: testWallet.balance
                    },
                    message: '测试钱包连接成功'
                }));
            } catch (error) {
                res.writeHead(400);
                res.end(JSON.stringify({
                    success: false,
                    error: error.message
                }));
            }
        });
        return;
    }
    
    // 获取余额
    if (url === '/api/wallet/balance') {
        res.writeHead(200);
        res.end(JSON.stringify({
            success: true,
            data: {
                address: testWallet.address,
                balances: testWallet.balance,
                network: 'devnet',
                lastUpdated: new Date().toISOString()
            }
        }));
        return;
    }
    
    // 空投测试代币
    if (url === '/api/wallet/airdrop' && req.method === 'POST') {
        testWallet.balance.SOL += 2;
        res.writeHead(200);
        res.end(JSON.stringify({
            success: true,
            data: {
                amount: 2,
                newBalance: testWallet.balance.SOL,
                txHash: 'test_airdrop_' + Date.now()
            },
            message: '测试SOL空投成功'
        }));
        return;
    }
    
    // 策略管理
    if (url === '/api/strategy') {
        if (req.method === 'GET') {
            res.writeHead(200);
            res.end(JSON.stringify({
                success: true,
                data: testStrategies,
                total: testStrategies.length,
                network: 'devnet'
            }));
        } else if (req.method === 'POST') {
            let body = '';
            req.on('data', chunk => body += chunk);
            req.on('end', () => {
                try {
                    const strategyData = JSON.parse(body);
                    const newStrategy = {
                        id: 'test_strategy_' + Date.now(),
                        ...strategyData,
                        network: 'devnet',
                        status: 'created',
                        testMode: true,
                        createdAt: new Date().toISOString()
                    };
                    
                    testStrategies.push(newStrategy);
                    
                    res.writeHead(200);
                    res.end(JSON.stringify({
                        success: true,
                        data: newStrategy,
                        message: '测试策略创建成功'
                    }));
                } catch (error) {
                    res.writeHead(400);
                    res.end(JSON.stringify({
                        success: false,
                        error: error.message
                    }));
                }
            });
        }
        return;
    }
    
    // 模拟交易
    if (url === '/api/transaction/simulate' && req.method === 'POST') {
        let body = '';
        req.on('data', chunk => body += chunk);
        req.on('end', () => {
            res.writeHead(200);
            res.end(JSON.stringify({
                success: true,
                data: {
                    simulation: true,
                    txHash: 'simulated_' + Date.now(),
                    gasUsed: Math.floor(Math.random() * 10000) + 5000,
                    status: 'success',
                    logs: ['Program log: Simulation successful']
                },
                message: '交易模拟成功'
            }));
        });
        return;
    }
    
    // Jupiter测试API
    if (url === '/api/jupiter/quote') {
        res.writeHead(200);
        res.end(JSON.stringify({
            success: true,
            data: {
                inputMint: 'So11111111111111111111111111111111111111112',
                outputMint: '4zMMC9srt5Ri5X14GAgXhaHii3GnPAEERYPJgZJDncDU',
                inAmount: '1000000',
                outAmount: '1500000',
                priceImpactPct: 0.1,
                testMode: true
            }
        }));
        return;
    }
    
    // 404处理
    res.writeHead(404);
    res.end(JSON.stringify({
        success: false,
        error: 'API endpoint not found',
        availableEndpoints: [
            '/api/health',
            '/api/network/status',
            '/api/wallet/connect',
            '/api/wallet/balance',
            '/api/wallet/airdrop',
            '/api/strategy',
            '/api/transaction/simulate',
            '/api/jupiter/quote'
        ]
    }));
}

// 启动服务器
server.listen(PORT, () => {
    console.log('');
    console.log('🧪 DLMM测试网服务器启动成功！');
    console.log('=' * 50);
    console.log(`🌐 测试网服务器: http://localhost:${PORT}`);
    console.log(`🔧 测试网设置: http://localhost:${PORT}/testnet-setup.html`);
    console.log(`🚀 策略创建器: http://localhost:${PORT}/strategy-creator.html`);
    console.log(`❤️ 健康检查: http://localhost:${PORT}/api/health`);
    console.log(`🌍 网络状态: http://localhost:${PORT}/api/network/status`);
    console.log('');
    console.log('🧪 测试网特性:');
    console.log('  ✅ 安全的测试环境');
    console.log('  ✅ 模拟交易功能');
    console.log('  ✅ 测试代币空投');
    console.log('  ✅ 完整API模拟');
    console.log('  ✅ 零风险测试');
    console.log('');
    console.log('🎯 现在可以开始测试了！');
});

server.on('error', (err) => {
    console.log('❌ 测试网服务器错误:', err.message);
    if (err.code === 'EADDRINUSE') {
        console.log('💡 端口被占用，尝试使用其他端口');
    }
});

process.on('SIGINT', () => {
    console.log('\n🛑 测试网服务器已停止');
    process.exit(0);
});
