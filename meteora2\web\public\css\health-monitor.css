/**
 * 🏥 健康监控组件样式
 */

.health-monitor {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 标题栏 */
.health-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.health-header h2 {
    margin: 0;
    font-size: 24px;
    font-weight: 600;
}

.health-controls {
    display: flex;
    gap: 10px;
}

/* 按钮样式 */
.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    gap: 6px;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.btn-primary {
    background: #3b82f6;
    color: white;
}

.btn-primary:hover {
    background: #2563eb;
}

.btn-secondary {
    background: #6b7280;
    color: white;
}

.btn-secondary:hover {
    background: #4b5563;
}

.btn-outline {
    background: transparent;
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.btn-outline:hover {
    background: rgba(255, 255, 255, 0.1);
}

.btn-warning {
    background: #f59e0b;
    color: white;
}

.btn-warning:hover {
    background: #d97706;
}

.btn-danger {
    background: #ef4444;
    color: white;
}

.btn-danger:hover {
    background: #dc2626;
}

.btn-sm {
    padding: 4px 8px;
    font-size: 12px;
}

/* 概览卡片 */
.health-overview {
    margin-bottom: 30px;
}

.overview-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.overview-card {
    background: white;
    padding: 20px;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
    transition: transform 0.2s ease;
}

.overview-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
}

.overview-card.status-card {
    border-left-width: 4px;
}

.card-title {
    font-size: 14px;
    color: #6b7280;
    margin-bottom: 8px;
    font-weight: 500;
}

.card-value {
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 4px;
}

.card-value.healthy {
    color: #10b981;
}

.card-value.warning {
    color: #f59e0b;
}

.card-value.error,
.card-value.critical {
    color: #ef4444;
}

.card-subtitle {
    font-size: 12px;
    color: #9ca3af;
}

/* 统计网格 */
.health-statistics {
    margin-bottom: 30px;
}

.health-statistics h3 {
    margin-bottom: 20px;
    color: #374151;
    font-size: 18px;
    font-weight: 600;
}

.statistics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    gap: 15px;
}

.stat-card {
    background: white;
    padding: 20px;
    border-radius: 10px;
    text-align: center;
    border: 2px solid transparent;
    transition: all 0.2s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.stat-card.healthy {
    border-color: #10b981;
    background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
}

.stat-card.warning {
    border-color: #f59e0b;
    background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
}

.stat-card.critical,
.stat-card.error {
    border-color: #ef4444;
    background: linear-gradient(135deg, #fef2f2 0%, #fecaca 100%);
}

.stat-card.total {
    border-color: #3b82f6;
    background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
}

.stat-card.issues {
    border-color: #f59e0b;
    background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
}

.stat-card.fixed {
    border-color: #10b981;
    background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
}

.stat-number {
    font-size: 28px;
    font-weight: 700;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 12px;
    color: #6b7280;
    font-weight: 500;
}

/* 实例健康 */
.health-instances {
    margin-bottom: 30px;
}

.health-instances h3 {
    margin-bottom: 20px;
    color: #374151;
    font-size: 18px;
    font-weight: 600;
}

.health-filter {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.health-filter select,
.health-filter input {
    padding: 8px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 14px;
    min-width: 150px;
}

.health-filter select:focus,
.health-filter input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* 实例健康卡片 */
.instance-health-card {
    background: white;
    border-radius: 12px;
    margin-bottom: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
    border: 1px solid #e5e7eb;
    overflow: hidden;
    transition: all 0.2s ease;
}

.instance-health-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
}

.instance-health-card.healthy {
    border-left: 4px solid #10b981;
}

.instance-health-card.warning {
    border-left: 4px solid #f59e0b;
}

.instance-health-card.critical,
.instance-health-card.error {
    border-left: 4px solid #ef4444;
}

.instance-header {
    padding: 20px;
    background: #f9fafb;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 10px;
}

.instance-id {
    font-size: 16px;
    font-weight: 600;
    color: #374151;
}

.instance-status {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
}

.instance-health-card.healthy .instance-status {
    background: #d1fae5;
    color: #065f46;
}

.instance-health-card.warning .instance-status {
    background: #fef3c7;
    color: #92400e;
}

.instance-health-card.critical .instance-status,
.instance-health-card.error .instance-status {
    background: #fecaca;
    color: #991b1b;
}

.instance-uptime {
    font-size: 12px;
    color: #6b7280;
}

.no-issues {
    padding: 20px;
    text-align: center;
    color: #10b981;
    font-weight: 500;
    background: #f0fdf4;
    margin: 0;
}

/* 问题列表 */
.instance-issues {
    padding: 20px;
}

.issues-header {
    font-size: 14px;
    font-weight: 600;
    color: #374151;
    margin-bottom: 15px;
}

.issue-item {
    background: #f9fafb;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 10px;
    border-left: 3px solid #d1d5db;
}

.issue-item.low {
    border-left-color: #3b82f6;
    background: #eff6ff;
}

.issue-item.medium {
    border-left-color: #f59e0b;
    background: #fffbeb;
}

.issue-item.high,
.issue-item.critical {
    border-left-color: #ef4444;
    background: #fef2f2;
}

.issue-type {
    font-size: 12px;
    font-weight: 600;
    color: #6b7280;
    text-transform: uppercase;
    margin-bottom: 5px;
}

.issue-description {
    font-size: 14px;
    color: #374151;
    margin-bottom: 8px;
}

.issue-fixed {
    font-size: 12px;
    color: #10b981;
    font-weight: 500;
}

.fix-issue {
    margin-top: 8px;
}

/* 实例操作 */
.instance-actions {
    padding: 15px 20px;
    background: #f9fafb;
    border-top: 1px solid #e5e7eb;
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

/* 历史记录 */
.health-history h3 {
    margin-bottom: 20px;
    color: #374151;
    font-size: 18px;
    font-weight: 600;
}

.history-header h4 {
    margin-bottom: 15px;
    color: #374151;
    font-size: 16px;
}

.history-list {
    background: white;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
    overflow: hidden;
}

.history-item {
    display: grid;
    grid-template-columns: 180px 100px 80px 80px;
    gap: 15px;
    padding: 15px 20px;
    border-bottom: 1px solid #f3f4f6;
    align-items: center;
    font-size: 14px;
}

.history-item:last-child {
    border-bottom: none;
}

.history-item.healthy {
    background: #f0fdf4;
}

.history-item.warning {
    background: #fffbeb;
}

.history-item.critical,
.history-item.error {
    background: #fef2f2;
}

.history-time {
    color: #374151;
    font-weight: 500;
}

.history-status {
    font-weight: 600;
}

.history-item.healthy .history-status {
    color: #059669;
}

.history-item.warning .history-status {
    color: #d97706;
}

.history-item.critical .history-status,
.history-item.error .history-status {
    color: #dc2626;
}

.history-issues,
.history-fixed {
    color: #6b7280;
    text-align: center;
}

/* 配置弹窗 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: white;
    border-radius: 12px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
}

.modal-header {
    padding: 20px;
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    font-size: 18px;
    color: #374151;
}

.modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #6b7280;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.modal-close:hover {
    background: #f3f4f6;
    color: #374151;
}

.modal-body {
    padding: 20px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #374151;
}

.form-group input {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 14px;
    box-sizing: border-box;
}

.form-group input[type="checkbox"] {
    width: auto;
    margin-right: 8px;
}

.form-group input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.modal-footer {
    padding: 15px 20px;
    border-top: 1px solid #e5e7eb;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

/* 加载和空状态 */
.loading,
.empty {
    text-align: center;
    padding: 40px 20px;
    color: #6b7280;
    font-style: italic;
}

.loading {
    color: #3b82f6;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .health-monitor {
        padding: 15px;
    }

    .health-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .health-controls {
        flex-wrap: wrap;
        justify-content: center;
    }

    .overview-cards {
        grid-template-columns: 1fr;
    }

    .statistics-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .instance-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .instance-actions {
        flex-direction: column;
    }

    .history-item {
        grid-template-columns: 1fr;
        gap: 8px;
        text-align: left;
    }

    .health-filter {
        flex-direction: column;
    }

    .health-filter select,
    .health-filter input {
        min-width: 100%;
    }
}