07/23 23:55:16 ERROR [strategy-chain_position_1753284004910_om4wiz] ERROR: ⚠️ 未找到连锁头寸bin范围数据，使用默认计算方法
07/23 23:55:16 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🏭 实例专用分析服务已创建 = {"instanceId":"chain_position_1753284004910_om4wiz","serviceType":"PositionAnalyticsService","dataIsolation":true,"factoryManaged":true}
07/23 23:55:16 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 头寸分析服务已设置 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2,"positionAddresses":["GT6A5nwf27sW4hKEXWzVukgYmu8vCcLygPW6XLMJWVUg","HHaJ5kxi2xY4eDdVFsqSQDvXc2FCvEK6jHS9rdExb6gC"],"initialInvestment":"8","yieldExtractionThreshold":"0.02"}
07/23 23:55:16 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🧠 智能止损模块已初始化 = {"instanceId":"chain_position_1753284004910_om4wiz","activeBinSafetyThreshold":30,"observationPeriodMinutes":5,"lossThresholdPercentage":2,"riskThreshold":70,"factoryManaged":true}
07/23 23:55:16 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🏗️ 头寸重建模块已初始化 = {"instanceId":"chain_position_1753284004910_om4wiz","outOfRangeTimeout":600,"maxPriceForRecreation":0.000001,"minPriceForRecreation":0.0000034,"enablePriceCheck":true,"enableMarketOpportunityRecreation":true,"enableLossRecoveryRecreation":true,"enableDynamicProfitRecreation":true,"marketOpportunity":{"positionThreshold":90,"profitThreshold":1.5},"lossRecovery":{"markPositionThreshold":40,"markLossThreshold":0.5,"triggerPositionThreshold":90,"triggerProfitThreshold":1.2},"dynamicProfitRecreation":{"positionThreshold":90,"benchmarkTier1Max":0.5,"benchmarkTier2Max":1.5,"benchmarkTier3Max":2.5,"benchmarkTier4Max":50,"profitThresholdTier1":0.5,"profitThresholdTier2":1.5,"profitThresholdTier3":2.5,"profitThresholdTier4":4},"minRecreationInterval":600000}
07/23 23:55:16 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔄 启动事件驱动监控系统 = {"monitoringInterval":12,"enableSmartStopLoss":true,"position1Address":"GT6A5nwf27sW4hKEXWzVukgYmu8vCcLygPW6XLMJWVUg","position2Address":"HHaJ5kxi2xY4eDdVFsqSQDvXc2FCvEK6jHS9rdExb6gC","analyticsServiceSetup":true}
07/23 23:55:16 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 策略实例已创建过连锁头寸，跳过重复创建 = {"position1Address":"GT6A5nwf27sW4hKEXWzVukgYmu8vCcLygPW6XLMJWVUg","position2Address":"HHaJ5kxi2xY4eDdVFsqSQDvXc2FCvEK6jHS9rdExb6gC","phase":"MONITORING","hasBeenCreated":true}
07/23 23:55:28 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-start = ┌──────────────────────────────────────────────────────────┐
07/23 23:55:28 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-title = │              连锁头寸策略监控 [0_om4wiz] - 23:55:28              │
07/23 23:55:28 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-separator = ├──────────────────────────────────────────────────────────┤
07/23 23:55:28 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔄 新轮询周期已启动 = {"pollingCycle":1,"pollingInterval":12000,"monitoringInterval":12}
07/23 23:55:29 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 开始调用PositionAnalyticsService获取分析数据 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2,"cycleId":1}
07/23 23:55:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ PositionAnalyticsService数据获取成功 = {"currentPrice":0.000003956545612985133,"positionValue":7.999998991903814,"netPnL":-0.0000010080961860836624,"netPnLPercentage":-0.00001260120232604578,"activeBin":-556,"positionLowerBin":-693,"positionUpperBin":-556,"holdingDuration":0.000001111111111111111}
07/23 23:55:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取开始 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/23 23:55:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/23 23:55:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 收益提取条件检查 = {"currentPendingYield":"0","yieldExtractionThreshold":"0.02","canExtract":false,"activeBin":-556,"positionLowerBin":-693,"positionUpperBin":-556}
07/23 23:55:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取完成 = {"currentPendingYield":"0","totalExtractedYield":"0"}
07/23 23:55:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/23 23:55:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🏗️ 头寸重建决策结果 = {"shouldRecreate":false,"reason":"No recreation needed (智能选择: 默认状态)","recreationType":"NONE","confidence":0,"urgency":"low","recommendedAction":"Continue monitoring","hasOutOfRangeDetails":false,"timeRemaining":null}
07/23 23:55:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🎯 智能状态选择详情 = {"selectedReason":"No recreation needed (智能选择: 默认状态)","stateType":"NONE","confidence":0}
07/23 23:55:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"condition":true}
07/23 23:55:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"isThresholdValid":true,"currentSwitchEnabled":false,"lastSwitchUpdateTime":"2025-07-23T15:55:04.891Z"}
07/23 23:55:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":1,"cacheAge":2,"reason":"防止重复调用PositionAnalyticsService"}
07/23 23:55:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 动态重建开关数据分析 = {"threshold":2,"isDataValid":false,"comparisonResult":"数据无效","willUpdateSwitch":false}
07/23 23:55:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 进入updateDynamicRecreationSwitch方法 = {"instanceId":"chain_position_1753284004910_om4wiz","configThreshold":2,"currentSwitchState":false}
07/23 23:55:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ⚠️ 基准收益率数据无效，跳过开关状态更新 = {"reason":"undefined值"}
07/23 23:55:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":1,"cacheAge":4,"reason":"防止重复调用PositionAnalyticsService"}
07/23 23:55:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🧠 智能止损分析完成 = {"决策行动":"继续持有","置信度":"90.00%","风险评分":"14.00","紧急程度":"低","分析原因":["位置安全: 活跃bin位置100.0%，高于30%安全线"]}
07/23 23:55:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📡 智能止损数据已广播到前端 = {"instanceId":"chain_position_1753284004910_om4wiz","action":"HOLD","confidence":90}
07/23 23:55:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ 策略继续运行 = {"置信度":"90.00%","风险评分":"14.00","策略状态":"正常运行中"}
07/23 23:55:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-end = └──────────────────────────────────────────────────────────┘
07/23 23:55:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-space = 
07/23 23:55:40 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-start = ┌──────────────────────────────────────────────────────────┐
07/23 23:55:40 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-title = │              连锁头寸策略监控 [0_om4wiz] - 23:55:40              │
07/23 23:55:40 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-separator = ├──────────────────────────────────────────────────────────┤
07/23 23:55:40 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔄 新轮询周期已启动 = {"pollingCycle":2,"pollingInterval":12000,"monitoringInterval":12}
07/23 23:55:41 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 开始调用PositionAnalyticsService获取分析数据 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2,"cycleId":2}
07/23 23:55:45 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ PositionAnalyticsService数据获取成功 = {"currentPrice":0.000003956545612985133,"positionValue":7.999998991903814,"netPnL":-0.0000010080961860836624,"netPnLPercentage":-0.00001260120232604578,"activeBin":-556,"positionLowerBin":-693,"positionUpperBin":-556,"holdingDuration":0.0035375}
07/23 23:55:45 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取开始 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/23 23:55:45 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/23 23:55:45 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 收益提取条件检查 = {"currentPendingYield":"0","yieldExtractionThreshold":"0.02","canExtract":false,"activeBin":-556,"positionLowerBin":-693,"positionUpperBin":-556}
07/23 23:55:45 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取完成 = {"currentPendingYield":"0","totalExtractedYield":"0"}
07/23 23:55:45 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/23 23:55:45 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🏗️ 头寸重建决策结果 = {"shouldRecreate":false,"reason":"No recreation needed (智能选择: 默认状态)","recreationType":"NONE","confidence":0,"urgency":"low","recommendedAction":"Continue monitoring","hasOutOfRangeDetails":false,"timeRemaining":null}
07/23 23:55:45 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🎯 智能状态选择详情 = {"selectedReason":"No recreation needed (智能选择: 默认状态)","stateType":"NONE","confidence":0}
07/23 23:55:45 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"condition":true}
07/23 23:55:45 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"isThresholdValid":true,"currentSwitchEnabled":false,"lastSwitchUpdateTime":"2025-07-23T15:55:04.891Z"}
07/23 23:55:45 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":2,"cacheAge":1,"reason":"防止重复调用PositionAnalyticsService"}
07/23 23:55:45 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 动态重建开关数据分析 = {"threshold":2,"isDataValid":false,"comparisonResult":"数据无效","willUpdateSwitch":false}
07/23 23:55:45 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 进入updateDynamicRecreationSwitch方法 = {"instanceId":"chain_position_1753284004910_om4wiz","configThreshold":2,"currentSwitchState":false}
07/23 23:55:45 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ⚠️ 基准收益率数据无效，跳过开关状态更新 = {"reason":"undefined值"}
07/23 23:55:45 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":2,"cacheAge":2,"reason":"防止重复调用PositionAnalyticsService"}
07/23 23:55:45 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🧠 智能止损分析完成 = {"决策行动":"继续持有","置信度":"90.00%","风险评分":"14.00","紧急程度":"低","分析原因":["位置安全: 活跃bin位置100.0%，高于30%安全线"]}
07/23 23:55:45 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📡 智能止损数据已广播到前端 = {"instanceId":"chain_position_1753284004910_om4wiz","action":"HOLD","confidence":90}
07/23 23:55:45 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ 策略继续运行 = {"置信度":"90.00%","风险评分":"14.00","策略状态":"正常运行中"}
07/23 23:55:45 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-end = └──────────────────────────────────────────────────────────┘
07/23 23:55:45 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-space = 
07/23 23:55:52 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-start = ┌──────────────────────────────────────────────────────────┐
07/23 23:55:52 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-title = │              连锁头寸策略监控 [0_om4wiz] - 23:55:52              │
07/23 23:55:52 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-separator = ├──────────────────────────────────────────────────────────┤
07/23 23:55:52 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔄 新轮询周期已启动 = {"pollingCycle":3,"pollingInterval":12000,"monitoringInterval":12}
07/23 23:55:53 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 开始调用PositionAnalyticsService获取分析数据 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2,"cycleId":3}
07/23 23:55:55 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ PositionAnalyticsService数据获取成功 = {"currentPrice":0.000003917371894044686,"positionValue":7.999998892974073,"netPnL":-0.000001107025926927463,"netPnLPercentage":-0.000013837824086593287,"activeBin":-557,"positionLowerBin":-693,"positionUpperBin":-556,"holdingDuration":0.006175}
07/23 23:55:55 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取开始 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/23 23:55:55 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/23 23:55:55 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 收益提取条件检查 = {"currentPendingYield":"0","yieldExtractionThreshold":"0.02","canExtract":false,"activeBin":-557,"positionLowerBin":-693,"positionUpperBin":-556}
07/23 23:55:55 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取完成 = {"currentPendingYield":"0","totalExtractedYield":"0"}
07/23 23:55:55 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/23 23:55:55 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🏗️ 头寸重建决策结果 = {"shouldRecreate":false,"reason":"No recreation needed (智能选择: 默认状态)","recreationType":"NONE","confidence":0,"urgency":"low","recommendedAction":"Continue monitoring","hasOutOfRangeDetails":false,"timeRemaining":null}
07/23 23:55:55 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🎯 智能状态选择详情 = {"selectedReason":"No recreation needed (智能选择: 默认状态)","stateType":"NONE","confidence":0}
07/23 23:55:55 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"condition":true}
07/23 23:55:55 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"isThresholdValid":true,"currentSwitchEnabled":false,"lastSwitchUpdateTime":"2025-07-23T15:55:04.891Z"}
07/23 23:55:55 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":3,"cacheAge":1,"reason":"防止重复调用PositionAnalyticsService"}
07/23 23:55:55 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 动态重建开关数据分析 = {"threshold":2,"isDataValid":false,"comparisonResult":"数据无效","willUpdateSwitch":false}
07/23 23:55:55 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 进入updateDynamicRecreationSwitch方法 = {"instanceId":"chain_position_1753284004910_om4wiz","configThreshold":2,"currentSwitchState":false}
07/23 23:55:55 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ⚠️ 基准收益率数据无效，跳过开关状态更新 = {"reason":"undefined值"}
07/23 23:55:55 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":3,"cacheAge":2,"reason":"防止重复调用PositionAnalyticsService"}
07/23 23:55:55 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🧠 智能止损分析完成 = {"决策行动":"继续持有","置信度":"90.00%","风险评分":"14.00","紧急程度":"低","分析原因":["位置安全: 活跃bin位置99.3%，高于30%安全线"]}
07/23 23:55:55 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📡 智能止损数据已广播到前端 = {"instanceId":"chain_position_1753284004910_om4wiz","action":"HOLD","confidence":90}
07/23 23:55:55 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ 策略继续运行 = {"置信度":"90.00%","风险评分":"14.00","策略状态":"正常运行中"}
07/23 23:55:55 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-end = └──────────────────────────────────────────────────────────┘
07/23 23:55:55 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-space = 
07/23 23:56:04 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-start = ┌──────────────────────────────────────────────────────────┐
07/23 23:56:04 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-title = │              连锁头寸策略监控 [0_om4wiz] - 23:56:04              │
07/23 23:56:04 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-separator = ├──────────────────────────────────────────────────────────┤
07/23 23:56:04 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔄 新轮询周期已启动 = {"pollingCycle":4,"pollingInterval":12000,"monitoringInterval":12}
07/23 23:56:05 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 开始调用PositionAnalyticsService获取分析数据 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2,"cycleId":4}
07/23 23:56:08 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ PositionAnalyticsService数据获取成功 = {"currentPrice":0.000003764517392206365,"positionValue":7.9997717995676485,"netPnL":0.0005615301602670542,"netPnLPercentage":0.007019127003338177,"activeBin":-561,"positionLowerBin":-693,"positionUpperBin":-556,"holdingDuration":0.009807222222222222}
07/23 23:56:08 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取开始 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/23 23:56:08 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/23 23:56:08 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 收益提取条件检查 = {"currentPendingYield":"0.000789730592617871","yieldExtractionThreshold":"0.02","canExtract":false,"activeBin":-561,"positionLowerBin":-693,"positionUpperBin":-556}
07/23 23:56:08 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取完成 = {"currentPendingYield":"0.000789730592617871","totalExtractedYield":"0"}
07/23 23:56:08 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/23 23:56:08 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🏗️ 头寸重建决策结果 = {"shouldRecreate":false,"reason":"No recreation needed (智能选择: 默认状态)","recreationType":"NONE","confidence":0,"urgency":"low","recommendedAction":"Continue monitoring","hasOutOfRangeDetails":false,"timeRemaining":null}
07/23 23:56:08 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🎯 智能状态选择详情 = {"selectedReason":"No recreation needed (智能选择: 默认状态)","stateType":"NONE","confidence":0}
07/23 23:56:08 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"condition":true}
07/23 23:56:08 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"isThresholdValid":true,"currentSwitchEnabled":false,"lastSwitchUpdateTime":"2025-07-23T15:55:04.891Z"}
07/23 23:56:08 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":4,"cacheAge":1,"reason":"防止重复调用PositionAnalyticsService"}
07/23 23:56:08 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 动态重建开关数据分析 = {"threshold":2,"isDataValid":false,"comparisonResult":"数据无效","willUpdateSwitch":false}
07/23 23:56:08 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 进入updateDynamicRecreationSwitch方法 = {"instanceId":"chain_position_1753284004910_om4wiz","configThreshold":2,"currentSwitchState":false}
07/23 23:56:08 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ⚠️ 基准收益率数据无效，跳过开关状态更新 = {"reason":"undefined值"}
07/23 23:56:08 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":4,"cacheAge":1,"reason":"防止重复调用PositionAnalyticsService"}
07/23 23:56:08 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🧠 智能止损分析完成 = {"决策行动":"继续持有","置信度":"90.00%","风险评分":"14.99","紧急程度":"低","分析原因":["位置安全: 活跃bin位置96.4%，高于30%安全线"]}
07/23 23:56:08 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📡 智能止损数据已广播到前端 = {"instanceId":"chain_position_1753284004910_om4wiz","action":"HOLD","confidence":90}
07/23 23:56:08 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ 策略继续运行 = {"置信度":"90.00%","风险评分":"14.99","策略状态":"正常运行中"}
07/23 23:56:08 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-end = └──────────────────────────────────────────────────────────┘
07/23 23:56:08 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-space = 
07/23 23:56:16 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-start = ┌──────────────────────────────────────────────────────────┐
07/23 23:56:16 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-title = │              连锁头寸策略监控 [0_om4wiz] - 23:56:16              │
07/23 23:56:16 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-separator = ├──────────────────────────────────────────────────────────┤
07/23 23:56:16 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔄 新轮询周期已启动 = {"pollingCycle":5,"pollingInterval":12000,"monitoringInterval":12}
07/23 23:56:17 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 开始调用PositionAnalyticsService获取分析数据 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2,"cycleId":5}
07/23 23:56:21 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ PositionAnalyticsService数据获取成功 = {"currentPrice":0.000003764517392206365,"positionValue":7.999775930778423,"netPnL":0.0005656613710414149,"netPnLPercentage":0.007070767138017686,"activeBin":-561,"positionLowerBin":-693,"positionUpperBin":-556,"holdingDuration":0.01338611111111111}
07/23 23:56:21 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取开始 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/23 23:56:21 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/23 23:56:21 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 收益提取条件检查 = {"currentPendingYield":"0.000789730592617871","yieldExtractionThreshold":"0.02","canExtract":false,"activeBin":-561,"positionLowerBin":-693,"positionUpperBin":-556}
07/23 23:56:21 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取完成 = {"currentPendingYield":"0.000789730592617871","totalExtractedYield":"0"}
07/23 23:56:21 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/23 23:56:21 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🏗️ 头寸重建决策结果 = {"shouldRecreate":false,"reason":"No recreation needed (智能选择: 默认状态)","recreationType":"NONE","confidence":0,"urgency":"low","recommendedAction":"Continue monitoring","hasOutOfRangeDetails":false,"timeRemaining":null}
07/23 23:56:21 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🎯 智能状态选择详情 = {"selectedReason":"No recreation needed (智能选择: 默认状态)","stateType":"NONE","confidence":0}
07/23 23:56:21 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"condition":true}
07/23 23:56:21 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"isThresholdValid":true,"currentSwitchEnabled":false,"lastSwitchUpdateTime":"2025-07-23T15:55:04.891Z"}
07/23 23:56:21 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":5,"cacheAge":1,"reason":"防止重复调用PositionAnalyticsService"}
07/23 23:56:21 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 动态重建开关数据分析 = {"threshold":2,"isDataValid":false,"comparisonResult":"数据无效","willUpdateSwitch":false}
07/23 23:56:21 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 进入updateDynamicRecreationSwitch方法 = {"instanceId":"chain_position_1753284004910_om4wiz","configThreshold":2,"currentSwitchState":false}
07/23 23:56:21 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ⚠️ 基准收益率数据无效，跳过开关状态更新 = {"reason":"undefined值"}
07/23 23:56:21 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":5,"cacheAge":2,"reason":"防止重复调用PositionAnalyticsService"}
07/23 23:56:21 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🧠 智能止损分析完成 = {"决策行动":"继续持有","置信度":"90.00%","风险评分":"17.90","紧急程度":"低","分析原因":["位置安全: 活跃bin位置96.4%，高于30%安全线"]}
07/23 23:56:21 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📡 智能止损数据已广播到前端 = {"instanceId":"chain_position_1753284004910_om4wiz","action":"HOLD","confidence":90}
07/23 23:56:21 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ 策略继续运行 = {"置信度":"90.00%","风险评分":"17.90","策略状态":"正常运行中"}
07/23 23:56:21 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-end = └──────────────────────────────────────────────────────────┘
07/23 23:56:21 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-space = 
07/23 23:56:28 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-start = ┌──────────────────────────────────────────────────────────┐
07/23 23:56:28 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-title = │              连锁头寸策略监控 [0_om4wiz] - 23:56:28              │
07/23 23:56:28 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-separator = ├──────────────────────────────────────────────────────────┤
07/23 23:56:28 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔄 新轮询周期已启动 = {"pollingCycle":6,"pollingInterval":12000,"monitoringInterval":12}
07/23 23:56:29 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 开始调用PositionAnalyticsService获取分析数据 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2,"cycleId":6}
07/23 23:56:34 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ PositionAnalyticsService数据获取成功 = {"currentPrice":0.000003764517392206365,"positionValue":7.999775930778423,"netPnL":0.0005656613710414149,"netPnLPercentage":0.007070767138017686,"activeBin":-561,"positionLowerBin":-693,"positionUpperBin":-556,"holdingDuration":0.01707111111111111}
07/23 23:56:34 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取开始 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/23 23:56:34 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/23 23:56:34 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 收益提取条件检查 = {"currentPendingYield":"0.000789730592617871","yieldExtractionThreshold":"0.02","canExtract":false,"activeBin":-561,"positionLowerBin":-693,"positionUpperBin":-556}
07/23 23:56:34 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取完成 = {"currentPendingYield":"0.000789730592617871","totalExtractedYield":"0"}
07/23 23:56:34 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/23 23:56:34 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🏗️ 头寸重建决策结果 = {"shouldRecreate":false,"reason":"No recreation needed (智能选择: 默认状态)","recreationType":"NONE","confidence":0,"urgency":"low","recommendedAction":"Continue monitoring","hasOutOfRangeDetails":false,"timeRemaining":null}
07/23 23:56:34 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🎯 智能状态选择详情 = {"selectedReason":"No recreation needed (智能选择: 默认状态)","stateType":"NONE","confidence":0}
07/23 23:56:34 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"condition":true}
07/23 23:56:34 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"isThresholdValid":true,"currentSwitchEnabled":false,"lastSwitchUpdateTime":"2025-07-23T15:55:04.891Z"}
07/23 23:56:34 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":6,"cacheAge":1,"reason":"防止重复调用PositionAnalyticsService"}
07/23 23:56:34 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 动态重建开关数据分析 = {"threshold":2,"isDataValid":false,"comparisonResult":"数据无效","willUpdateSwitch":false}
07/23 23:56:34 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 进入updateDynamicRecreationSwitch方法 = {"instanceId":"chain_position_1753284004910_om4wiz","configThreshold":2,"currentSwitchState":false}
07/23 23:56:34 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ⚠️ 基准收益率数据无效，跳过开关状态更新 = {"reason":"undefined值"}
07/23 23:56:34 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":6,"cacheAge":2,"reason":"防止重复调用PositionAnalyticsService"}
07/23 23:56:34 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🧠 智能止损分析完成 = {"决策行动":"继续持有","置信度":"90.00%","风险评分":"14.00","紧急程度":"低","分析原因":["位置安全: 活跃bin位置96.4%，高于30%安全线"]}
07/23 23:56:34 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📡 智能止损数据已广播到前端 = {"instanceId":"chain_position_1753284004910_om4wiz","action":"HOLD","confidence":90}
07/23 23:56:34 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ 策略继续运行 = {"置信度":"90.00%","风险评分":"14.00","策略状态":"正常运行中"}
07/23 23:56:34 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-end = └──────────────────────────────────────────────────────────┘
07/23 23:56:34 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-space = 
07/23 23:56:40 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-start = ┌──────────────────────────────────────────────────────────┐
07/23 23:56:40 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-title = │              连锁头寸策略监控 [0_om4wiz] - 23:56:40              │
07/23 23:56:40 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-separator = ├──────────────────────────────────────────────────────────┤
07/23 23:56:40 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔄 新轮询周期已启动 = {"pollingCycle":7,"pollingInterval":12000,"monitoringInterval":12}
07/23 23:56:41 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 开始调用PositionAnalyticsService获取分析数据 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2,"cycleId":7}
07/23 23:56:46 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ PositionAnalyticsService数据获取成功 = {"currentPrice":0.000003764517392206365,"positionValue":7.999775930778423,"netPnL":0.0005656613710414149,"netPnLPercentage":0.007070767138017686,"activeBin":-561,"positionLowerBin":-693,"positionUpperBin":-556,"holdingDuration":0.020441388888888887}
07/23 23:56:46 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取开始 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/23 23:56:46 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/23 23:56:46 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 收益提取条件检查 = {"currentPendingYield":"0.000789730592617871","yieldExtractionThreshold":"0.02","canExtract":false,"activeBin":-561,"positionLowerBin":-693,"positionUpperBin":-556}
07/23 23:56:46 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取完成 = {"currentPendingYield":"0.000789730592617871","totalExtractedYield":"0"}
07/23 23:56:46 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/23 23:56:46 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🏗️ 头寸重建决策结果 = {"shouldRecreate":false,"reason":"No recreation needed (智能选择: 默认状态)","recreationType":"NONE","confidence":0,"urgency":"low","recommendedAction":"Continue monitoring","hasOutOfRangeDetails":false,"timeRemaining":null}
07/23 23:56:46 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🎯 智能状态选择详情 = {"selectedReason":"No recreation needed (智能选择: 默认状态)","stateType":"NONE","confidence":0}
07/23 23:56:46 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"condition":true}
07/23 23:56:46 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"isThresholdValid":true,"currentSwitchEnabled":false,"lastSwitchUpdateTime":"2025-07-23T15:55:04.891Z"}
07/23 23:56:46 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":7,"cacheAge":2,"reason":"防止重复调用PositionAnalyticsService"}
07/23 23:56:46 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 动态重建开关数据分析 = {"threshold":2,"isDataValid":false,"comparisonResult":"数据无效","willUpdateSwitch":false}
07/23 23:56:46 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 进入updateDynamicRecreationSwitch方法 = {"instanceId":"chain_position_1753284004910_om4wiz","configThreshold":2,"currentSwitchState":false}
07/23 23:56:46 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ⚠️ 基准收益率数据无效，跳过开关状态更新 = {"reason":"undefined值"}
07/23 23:56:46 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":7,"cacheAge":3,"reason":"防止重复调用PositionAnalyticsService"}
07/23 23:56:46 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🧠 智能止损分析完成 = {"决策行动":"继续持有","置信度":"90.00%","风险评分":"14.00","紧急程度":"低","分析原因":["位置安全: 活跃bin位置96.4%，高于30%安全线"]}
07/23 23:56:46 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📡 智能止损数据已广播到前端 = {"instanceId":"chain_position_1753284004910_om4wiz","action":"HOLD","confidence":90}
07/23 23:56:46 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ 策略继续运行 = {"置信度":"90.00%","风险评分":"14.00","策略状态":"正常运行中"}
07/23 23:56:46 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-end = └──────────────────────────────────────────────────────────┘
07/23 23:56:46 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-space = 
07/23 23:56:52 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-start = ┌──────────────────────────────────────────────────────────┐
07/23 23:56:52 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-title = │              连锁头寸策略监控 [0_om4wiz] - 23:56:52              │
07/23 23:56:52 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-separator = ├──────────────────────────────────────────────────────────┤
07/23 23:56:52 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔄 新轮询周期已启动 = {"pollingCycle":8,"pollingInterval":12000,"monitoringInterval":12}
07/23 23:56:53 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 开始调用PositionAnalyticsService获取分析数据 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2,"cycleId":8}
07/23 23:56:56 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ PositionAnalyticsService数据获取成功 = {"currentPrice":0.00000387858603370761,"positionValue":7.9999786967146225,"netPnL":0.0014664569339277733,"netPnLPercentage":0.018330711674097167,"activeBin":-558,"positionLowerBin":-693,"positionUpperBin":-556,"holdingDuration":0.023245277777777778}
07/23 23:56:56 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取开始 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/23 23:56:56 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/23 23:56:58 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 收益提取条件检查 = {"currentPendingYield":"0.0014877602193047851","yieldExtractionThreshold":"0.02","canExtract":false,"activeBin":-558,"positionLowerBin":-693,"positionUpperBin":-556}
07/23 23:56:58 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取完成 = {"currentPendingYield":"0.0014877602193047851","totalExtractedYield":"0"}
07/23 23:56:58 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/23 23:56:58 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🏗️ 头寸重建决策结果 = {"shouldRecreate":false,"reason":"No recreation needed (智能选择: 默认状态)","recreationType":"NONE","confidence":0,"urgency":"low","recommendedAction":"Continue monitoring","hasOutOfRangeDetails":false,"timeRemaining":null}
07/23 23:56:58 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🎯 智能状态选择详情 = {"selectedReason":"No recreation needed (智能选择: 默认状态)","stateType":"NONE","confidence":0}
07/23 23:56:58 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"condition":true}
07/23 23:56:58 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"isThresholdValid":true,"currentSwitchEnabled":false,"lastSwitchUpdateTime":"2025-07-23T15:55:04.891Z"}
07/23 23:56:58 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":8,"cacheAge":2,"reason":"防止重复调用PositionAnalyticsService"}
07/23 23:56:58 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 动态重建开关数据分析 = {"threshold":2,"isDataValid":false,"comparisonResult":"数据无效","willUpdateSwitch":false}
07/23 23:56:58 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 进入updateDynamicRecreationSwitch方法 = {"instanceId":"chain_position_1753284004910_om4wiz","configThreshold":2,"currentSwitchState":false}
07/23 23:56:58 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ⚠️ 基准收益率数据无效，跳过开关状态更新 = {"reason":"undefined值"}
07/23 23:56:58 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":8,"cacheAge":3,"reason":"防止重复调用PositionAnalyticsService"}
07/23 23:56:58 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🧠 智能止损分析完成 = {"决策行动":"继续持有","置信度":"90.00%","风险评分":"12.00","紧急程度":"低","分析原因":["位置安全: 活跃bin位置98.5%，高于30%安全线"]}
07/23 23:56:58 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📡 智能止损数据已广播到前端 = {"instanceId":"chain_position_1753284004910_om4wiz","action":"HOLD","confidence":90}
07/23 23:56:58 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ 策略继续运行 = {"置信度":"90.00%","风险评分":"12.00","策略状态":"正常运行中"}
07/23 23:56:58 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-end = └──────────────────────────────────────────────────────────┘
07/23 23:56:58 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-space = 
07/23 23:57:04 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-start = ┌──────────────────────────────────────────────────────────┐
07/23 23:57:04 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-title = │              连锁头寸策略监控 [0_om4wiz] - 23:57:04              │
07/23 23:57:04 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-separator = ├──────────────────────────────────────────────────────────┤
07/23 23:57:04 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔄 新轮询周期已启动 = {"pollingCycle":9,"pollingInterval":12000,"monitoringInterval":12}
07/23 23:57:05 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 开始调用PositionAnalyticsService获取分析数据 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2,"cycleId":9}
07/23 23:57:09 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ PositionAnalyticsService数据获取成功 = {"currentPrice":0.00000387858603370761,"positionValue":7.9999786967146225,"netPnL":0.0014664569339277733,"netPnLPercentage":0.018330711674097167,"activeBin":-558,"positionLowerBin":-693,"positionUpperBin":-556,"holdingDuration":0.02670222222222222}
07/23 23:57:09 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取开始 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/23 23:57:09 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/23 23:57:11 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 收益提取条件检查 = {"currentPendingYield":"0.0014877602193047851","yieldExtractionThreshold":"0.02","canExtract":false,"activeBin":-558,"positionLowerBin":-693,"positionUpperBin":-556}
07/23 23:57:11 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取完成 = {"currentPendingYield":"0.0014877602193047851","totalExtractedYield":"0"}
07/23 23:57:11 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/23 23:57:11 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🏗️ 头寸重建决策结果 = {"shouldRecreate":false,"reason":"No recreation needed (智能选择: 默认状态)","recreationType":"NONE","confidence":0,"urgency":"low","recommendedAction":"Continue monitoring","hasOutOfRangeDetails":false,"timeRemaining":null}
07/23 23:57:11 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🎯 智能状态选择详情 = {"selectedReason":"No recreation needed (智能选择: 默认状态)","stateType":"NONE","confidence":0}
07/23 23:57:11 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"condition":true}
07/23 23:57:11 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"isThresholdValid":true,"currentSwitchEnabled":false,"lastSwitchUpdateTime":"2025-07-23T15:55:04.891Z"}
07/23 23:57:11 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":9,"cacheAge":3,"reason":"防止重复调用PositionAnalyticsService"}
07/23 23:57:11 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 动态重建开关数据分析 = {"threshold":2,"isDataValid":false,"comparisonResult":"数据无效","willUpdateSwitch":false}
07/23 23:57:11 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 进入updateDynamicRecreationSwitch方法 = {"instanceId":"chain_position_1753284004910_om4wiz","configThreshold":2,"currentSwitchState":false}
07/23 23:57:11 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ⚠️ 基准收益率数据无效，跳过开关状态更新 = {"reason":"undefined值"}
07/23 23:57:11 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":9,"cacheAge":4,"reason":"防止重复调用PositionAnalyticsService"}
07/23 23:57:11 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🧠 智能止损分析完成 = {"决策行动":"继续持有","置信度":"90.00%","风险评分":"15.03","紧急程度":"低","分析原因":["位置安全: 活跃bin位置98.5%，高于30%安全线"]}
07/23 23:57:11 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📡 智能止损数据已广播到前端 = {"instanceId":"chain_position_1753284004910_om4wiz","action":"HOLD","confidence":90}
07/23 23:57:11 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ 策略继续运行 = {"置信度":"90.00%","风险评分":"15.03","策略状态":"正常运行中"}
07/23 23:57:11 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-end = └──────────────────────────────────────────────────────────┘
07/23 23:57:11 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-space = 
07/23 23:57:16 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-start = ┌──────────────────────────────────────────────────────────┐
07/23 23:57:16 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-title = │              连锁头寸策略监控 [0_om4wiz] - 23:57:16              │
07/23 23:57:16 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-separator = ├──────────────────────────────────────────────────────────┤
07/23 23:57:16 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔄 新轮询周期已启动 = {"pollingCycle":10,"pollingInterval":12000,"monitoringInterval":12}
07/23 23:57:17 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 开始调用PositionAnalyticsService获取分析数据 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2,"cycleId":10}
07/23 23:57:18 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ PositionAnalyticsService数据获取成功 = {"currentPrice":0.00000387858603370761,"positionValue":7.9999786967146225,"netPnL":0.0014664569339277733,"netPnLPercentage":0.018330711674097167,"activeBin":-558,"positionLowerBin":-693,"positionUpperBin":-556,"holdingDuration":0.02941111111111111}
07/23 23:57:18 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取开始 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/23 23:57:18 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/23 23:57:18 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 收益提取条件检查 = {"currentPendingYield":"0.0014877602193047851","yieldExtractionThreshold":"0.02","canExtract":false,"activeBin":-558,"positionLowerBin":-693,"positionUpperBin":-556}
07/23 23:57:18 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取完成 = {"currentPendingYield":"0.0014877602193047851","totalExtractedYield":"0"}
07/23 23:57:18 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/23 23:57:18 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🏗️ 头寸重建决策结果 = {"shouldRecreate":false,"reason":"No recreation needed (智能选择: 默认状态)","recreationType":"NONE","confidence":0,"urgency":"low","recommendedAction":"Continue monitoring","hasOutOfRangeDetails":false,"timeRemaining":null}
07/23 23:57:18 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🎯 智能状态选择详情 = {"selectedReason":"No recreation needed (智能选择: 默认状态)","stateType":"NONE","confidence":0}
07/23 23:57:18 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"condition":true}
07/23 23:57:18 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"isThresholdValid":true,"currentSwitchEnabled":false,"lastSwitchUpdateTime":"2025-07-23T15:55:04.891Z"}
07/23 23:57:18 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":10,"cacheAge":1,"reason":"防止重复调用PositionAnalyticsService"}
07/23 23:57:18 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 动态重建开关数据分析 = {"threshold":2,"isDataValid":false,"comparisonResult":"数据无效","willUpdateSwitch":false}
07/23 23:57:18 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 进入updateDynamicRecreationSwitch方法 = {"instanceId":"chain_position_1753284004910_om4wiz","configThreshold":2,"currentSwitchState":false}
07/23 23:57:18 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ⚠️ 基准收益率数据无效，跳过开关状态更新 = {"reason":"undefined值"}
07/23 23:57:18 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":10,"cacheAge":2,"reason":"防止重复调用PositionAnalyticsService"}
07/23 23:57:18 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🧠 智能止损分析完成 = {"决策行动":"继续持有","置信度":"90.00%","风险评分":"12.00","紧急程度":"低","分析原因":["位置安全: 活跃bin位置98.5%，高于30%安全线"]}
07/23 23:57:18 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📡 智能止损数据已广播到前端 = {"instanceId":"chain_position_1753284004910_om4wiz","action":"HOLD","confidence":90}
07/23 23:57:18 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ 策略继续运行 = {"置信度":"90.00%","风险评分":"12.00","策略状态":"正常运行中"}
07/23 23:57:18 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-end = └──────────────────────────────────────────────────────────┘
07/23 23:57:18 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-space = 
07/23 23:57:28 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-start = ┌──────────────────────────────────────────────────────────┐
07/23 23:57:28 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-title = │              连锁头寸策略监控 [0_om4wiz] - 23:57:28              │
07/23 23:57:28 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-separator = ├──────────────────────────────────────────────────────────┤
07/23 23:57:28 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔄 新轮询周期已启动 = {"pollingCycle":11,"pollingInterval":12000,"monitoringInterval":12}
07/23 23:57:29 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 开始调用PositionAnalyticsService获取分析数据 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2,"cycleId":11}
07/23 23:57:33 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ PositionAnalyticsService数据获取成功 = {"currentPrice":0.00000387858603370761,"positionValue":7.9999786967146225,"netPnL":0.0014664569339277733,"netPnLPercentage":0.018330711674097167,"activeBin":-558,"positionLowerBin":-693,"positionUpperBin":-556,"holdingDuration":0.033479166666666664}
07/23 23:57:33 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取开始 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/23 23:57:33 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/23 23:57:33 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 收益提取条件检查 = {"currentPendingYield":"0.0014877602193047851","yieldExtractionThreshold":"0.02","canExtract":false,"activeBin":-558,"positionLowerBin":-693,"positionUpperBin":-556}
07/23 23:57:33 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取完成 = {"currentPendingYield":"0.0014877602193047851","totalExtractedYield":"0"}
07/23 23:57:33 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/23 23:57:33 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🏗️ 头寸重建决策结果 = {"shouldRecreate":false,"reason":"No recreation needed (智能选择: 默认状态)","recreationType":"NONE","confidence":0,"urgency":"low","recommendedAction":"Continue monitoring","hasOutOfRangeDetails":false,"timeRemaining":null}
07/23 23:57:33 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🎯 智能状态选择详情 = {"selectedReason":"No recreation needed (智能选择: 默认状态)","stateType":"NONE","confidence":0}
07/23 23:57:33 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"condition":true}
07/23 23:57:33 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"isThresholdValid":true,"currentSwitchEnabled":false,"lastSwitchUpdateTime":"2025-07-23T15:55:04.891Z"}
07/23 23:57:33 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":11,"cacheAge":1,"reason":"防止重复调用PositionAnalyticsService"}
07/23 23:57:33 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 动态重建开关数据分析 = {"threshold":2,"isDataValid":false,"comparisonResult":"数据无效","willUpdateSwitch":false}
07/23 23:57:33 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 进入updateDynamicRecreationSwitch方法 = {"instanceId":"chain_position_1753284004910_om4wiz","configThreshold":2,"currentSwitchState":false}
07/23 23:57:33 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ⚠️ 基准收益率数据无效，跳过开关状态更新 = {"reason":"undefined值"}
07/23 23:57:33 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":11,"cacheAge":2,"reason":"防止重复调用PositionAnalyticsService"}
07/23 23:57:33 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🧠 智能止损分析完成 = {"决策行动":"继续持有","置信度":"90.00%","风险评分":"12.00","紧急程度":"低","分析原因":["位置安全: 活跃bin位置98.5%，高于30%安全线"]}
07/23 23:57:33 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📡 智能止损数据已广播到前端 = {"instanceId":"chain_position_1753284004910_om4wiz","action":"HOLD","confidence":90}
07/23 23:57:33 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ 策略继续运行 = {"置信度":"90.00%","风险评分":"12.00","策略状态":"正常运行中"}
07/23 23:57:33 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-end = └──────────────────────────────────────────────────────────┘
07/23 23:57:33 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-space = 
07/23 23:57:40 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-start = ┌──────────────────────────────────────────────────────────┐
07/23 23:57:40 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-title = │              连锁头寸策略监控 [0_om4wiz] - 23:57:40              │
07/23 23:57:40 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-separator = ├──────────────────────────────────────────────────────────┤
07/23 23:57:40 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔄 新轮询周期已启动 = {"pollingCycle":12,"pollingInterval":12000,"monitoringInterval":12}
07/23 23:57:41 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 开始调用PositionAnalyticsService获取分析数据 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2,"cycleId":12}
07/23 23:57:44 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ PositionAnalyticsService数据获取成功 = {"currentPrice":0.00000387858603370761,"positionValue":7.9999786967146225,"netPnL":0.0014664569339277733,"netPnLPercentage":0.018330711674097167,"activeBin":-558,"positionLowerBin":-693,"positionUpperBin":-556,"holdingDuration":0.036618333333333336}
07/23 23:57:44 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取开始 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/23 23:57:44 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/23 23:57:44 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 收益提取条件检查 = {"currentPendingYield":"0.0014877602193047851","yieldExtractionThreshold":"0.02","canExtract":false,"activeBin":-558,"positionLowerBin":-693,"positionUpperBin":-556}
07/23 23:57:44 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取完成 = {"currentPendingYield":"0.0014877602193047851","totalExtractedYield":"0"}
07/23 23:57:44 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/23 23:57:44 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🏗️ 头寸重建决策结果 = {"shouldRecreate":false,"reason":"No recreation needed (智能选择: 默认状态)","recreationType":"NONE","confidence":0,"urgency":"low","recommendedAction":"Continue monitoring","hasOutOfRangeDetails":false,"timeRemaining":null}
07/23 23:57:44 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🎯 智能状态选择详情 = {"selectedReason":"No recreation needed (智能选择: 默认状态)","stateType":"NONE","confidence":0}
07/23 23:57:44 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"condition":true}
07/23 23:57:44 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"isThresholdValid":true,"currentSwitchEnabled":false,"lastSwitchUpdateTime":"2025-07-23T15:55:04.891Z"}
07/23 23:57:44 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":12,"cacheAge":1,"reason":"防止重复调用PositionAnalyticsService"}
07/23 23:57:44 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 动态重建开关数据分析 = {"threshold":2,"isDataValid":false,"comparisonResult":"数据无效","willUpdateSwitch":false}
07/23 23:57:44 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 进入updateDynamicRecreationSwitch方法 = {"instanceId":"chain_position_1753284004910_om4wiz","configThreshold":2,"currentSwitchState":false}
07/23 23:57:44 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ⚠️ 基准收益率数据无效，跳过开关状态更新 = {"reason":"undefined值"}
07/23 23:57:44 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":12,"cacheAge":2,"reason":"防止重复调用PositionAnalyticsService"}
07/23 23:57:44 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🧠 智能止损分析完成 = {"决策行动":"继续持有","置信度":"90.00%","风险评分":"14.00","紧急程度":"低","分析原因":["位置安全: 活跃bin位置98.5%，高于30%安全线"]}
07/23 23:57:44 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📡 智能止损数据已广播到前端 = {"instanceId":"chain_position_1753284004910_om4wiz","action":"HOLD","confidence":90}
07/23 23:57:44 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ 策略继续运行 = {"置信度":"90.00%","风险评分":"14.00","策略状态":"正常运行中"}
07/23 23:57:44 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-end = └──────────────────────────────────────────────────────────┘
07/23 23:57:44 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-space = 
07/23 23:57:52 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-start = ┌──────────────────────────────────────────────────────────┐
07/23 23:57:52 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-title = │              连锁头寸策略监控 [0_om4wiz] - 23:57:52              │
07/23 23:57:52 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-separator = ├──────────────────────────────────────────────────────────┤
07/23 23:57:52 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔄 新轮询周期已启动 = {"pollingCycle":13,"pollingInterval":12000,"monitoringInterval":12}
07/23 23:57:53 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 开始调用PositionAnalyticsService获取分析数据 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2,"cycleId":13}
07/23 23:57:58 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ PositionAnalyticsService数据获取成功 = {"currentPrice":0.00000387858603370761,"positionValue":7.9999786967146225,"netPnL":0.0014664569339277733,"netPnLPercentage":0.018330711674097167,"activeBin":-558,"positionLowerBin":-693,"positionUpperBin":-556,"holdingDuration":0.04038777777777778}
07/23 23:57:58 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取开始 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/23 23:57:58 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/23 23:57:58 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 收益提取条件检查 = {"currentPendingYield":"0.0014877602193047851","yieldExtractionThreshold":"0.02","canExtract":false,"activeBin":-558,"positionLowerBin":-693,"positionUpperBin":-556}
07/23 23:57:58 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取完成 = {"currentPendingYield":"0.0014877602193047851","totalExtractedYield":"0"}
07/23 23:57:58 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/23 23:57:58 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🏗️ 头寸重建决策结果 = {"shouldRecreate":false,"reason":"No recreation needed (智能选择: 默认状态)","recreationType":"NONE","confidence":0,"urgency":"low","recommendedAction":"Continue monitoring","hasOutOfRangeDetails":false,"timeRemaining":null}
07/23 23:57:58 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🎯 智能状态选择详情 = {"selectedReason":"No recreation needed (智能选择: 默认状态)","stateType":"NONE","confidence":0}
07/23 23:57:58 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"condition":true}
07/23 23:57:58 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"isThresholdValid":true,"currentSwitchEnabled":false,"lastSwitchUpdateTime":"2025-07-23T15:55:04.891Z"}
07/23 23:57:58 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":13,"cacheAge":1,"reason":"防止重复调用PositionAnalyticsService"}
07/23 23:57:58 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 动态重建开关数据分析 = {"threshold":2,"isDataValid":false,"comparisonResult":"数据无效","willUpdateSwitch":false}
07/23 23:57:58 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 进入updateDynamicRecreationSwitch方法 = {"instanceId":"chain_position_1753284004910_om4wiz","configThreshold":2,"currentSwitchState":false}
07/23 23:57:58 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ⚠️ 基准收益率数据无效，跳过开关状态更新 = {"reason":"undefined值"}
07/23 23:57:58 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":13,"cacheAge":1,"reason":"防止重复调用PositionAnalyticsService"}
07/23 23:57:58 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🧠 智能止损分析完成 = {"决策行动":"继续持有","置信度":"90.00%","风险评分":"14.00","紧急程度":"低","分析原因":["位置安全: 活跃bin位置98.5%，高于30%安全线"]}
07/23 23:57:58 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📡 智能止损数据已广播到前端 = {"instanceId":"chain_position_1753284004910_om4wiz","action":"HOLD","confidence":90}
07/23 23:57:58 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ 策略继续运行 = {"置信度":"90.00%","风险评分":"14.00","策略状态":"正常运行中"}
07/23 23:57:58 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-end = └──────────────────────────────────────────────────────────┘
07/23 23:57:58 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-space = 
07/23 23:58:04 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-start = ┌──────────────────────────────────────────────────────────┐
07/23 23:58:04 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-title = │              连锁头寸策略监控 [0_om4wiz] - 23:58:04              │
07/23 23:58:04 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-separator = ├──────────────────────────────────────────────────────────┤
07/23 23:58:04 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔄 新轮询周期已启动 = {"pollingCycle":14,"pollingInterval":12000,"monitoringInterval":12}
07/23 23:58:05 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 开始调用PositionAnalyticsService获取分析数据 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2,"cycleId":14}
07/23 23:58:10 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ PositionAnalyticsService数据获取成功 = {"currentPrice":0.00000387858603370761,"positionValue":7.9999786967146225,"netPnL":0.0014664569339277733,"netPnLPercentage":0.018330711674097167,"activeBin":-558,"positionLowerBin":-693,"positionUpperBin":-556,"holdingDuration":0.043866666666666665}
07/23 23:58:10 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取开始 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/23 23:58:10 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/23 23:58:10 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 收益提取条件检查 = {"currentPendingYield":"0.0014877602193047851","yieldExtractionThreshold":"0.02","canExtract":false,"activeBin":-558,"positionLowerBin":-693,"positionUpperBin":-556}
07/23 23:58:10 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取完成 = {"currentPendingYield":"0.0014877602193047851","totalExtractedYield":"0"}
07/23 23:58:10 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/23 23:58:10 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🏗️ 头寸重建决策结果 = {"shouldRecreate":false,"reason":"No recreation needed (智能选择: 默认状态)","recreationType":"NONE","confidence":0,"urgency":"low","recommendedAction":"Continue monitoring","hasOutOfRangeDetails":false,"timeRemaining":null}
07/23 23:58:10 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🎯 智能状态选择详情 = {"selectedReason":"No recreation needed (智能选择: 默认状态)","stateType":"NONE","confidence":0}
07/23 23:58:10 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"condition":true}
07/23 23:58:10 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"isThresholdValid":true,"currentSwitchEnabled":false,"lastSwitchUpdateTime":"2025-07-23T15:55:04.891Z"}
07/23 23:58:10 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":14,"cacheAge":1,"reason":"防止重复调用PositionAnalyticsService"}
07/23 23:58:10 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 动态重建开关数据分析 = {"threshold":2,"isDataValid":false,"comparisonResult":"数据无效","willUpdateSwitch":false}
07/23 23:58:10 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 进入updateDynamicRecreationSwitch方法 = {"instanceId":"chain_position_1753284004910_om4wiz","configThreshold":2,"currentSwitchState":false}
07/23 23:58:10 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ⚠️ 基准收益率数据无效，跳过开关状态更新 = {"reason":"undefined值"}
07/23 23:58:10 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":14,"cacheAge":2,"reason":"防止重复调用PositionAnalyticsService"}
07/23 23:58:10 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🧠 智能止损分析完成 = {"决策行动":"继续持有","置信度":"90.00%","风险评分":"14.00","紧急程度":"低","分析原因":["位置安全: 活跃bin位置98.5%，高于30%安全线"]}
07/23 23:58:10 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📡 智能止损数据已广播到前端 = {"instanceId":"chain_position_1753284004910_om4wiz","action":"HOLD","confidence":90}
07/23 23:58:10 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ 策略继续运行 = {"置信度":"90.00%","风险评分":"14.00","策略状态":"正常运行中"}
07/23 23:58:10 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-end = └──────────────────────────────────────────────────────────┘
07/23 23:58:10 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-space = 
07/23 23:58:16 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-start = ┌──────────────────────────────────────────────────────────┐
07/23 23:58:16 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-title = │              连锁头寸策略监控 [0_om4wiz] - 23:58:16              │
07/23 23:58:16 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-separator = ├──────────────────────────────────────────────────────────┤
07/23 23:58:16 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔄 新轮询周期已启动 = {"pollingCycle":15,"pollingInterval":12000,"monitoringInterval":12}
07/23 23:58:17 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 开始调用PositionAnalyticsService获取分析数据 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2,"cycleId":15}
07/23 23:58:20 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ PositionAnalyticsService数据获取成功 = {"currentPrice":0.00000387858603370761,"positionValue":7.9999786967146225,"netPnL":0.0014664569339277733,"netPnLPercentage":0.018330711674097167,"activeBin":-558,"positionLowerBin":-693,"positionUpperBin":-556,"holdingDuration":0.04665111111111111}
07/23 23:58:20 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取开始 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/23 23:58:20 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/23 23:58:22 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 收益提取条件检查 = {"currentPendingYield":"0.0014877602193047851","yieldExtractionThreshold":"0.02","canExtract":false,"activeBin":-558,"positionLowerBin":-693,"positionUpperBin":-556}
07/23 23:58:22 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取完成 = {"currentPendingYield":"0.0014877602193047851","totalExtractedYield":"0"}
07/23 23:58:22 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/23 23:58:22 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🏗️ 头寸重建决策结果 = {"shouldRecreate":false,"reason":"No recreation needed (智能选择: 默认状态)","recreationType":"NONE","confidence":0,"urgency":"low","recommendedAction":"Continue monitoring","hasOutOfRangeDetails":false,"timeRemaining":null}
07/23 23:58:22 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🎯 智能状态选择详情 = {"selectedReason":"No recreation needed (智能选择: 默认状态)","stateType":"NONE","confidence":0}
07/23 23:58:22 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"condition":true}
07/23 23:58:22 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"isThresholdValid":true,"currentSwitchEnabled":false,"lastSwitchUpdateTime":"2025-07-23T15:55:04.891Z"}
07/23 23:58:22 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":15,"cacheAge":1,"reason":"防止重复调用PositionAnalyticsService"}
07/23 23:58:22 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 动态重建开关数据分析 = {"threshold":2,"isDataValid":false,"comparisonResult":"数据无效","willUpdateSwitch":false}
07/23 23:58:22 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 进入updateDynamicRecreationSwitch方法 = {"instanceId":"chain_position_1753284004910_om4wiz","configThreshold":2,"currentSwitchState":false}
07/23 23:58:22 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ⚠️ 基准收益率数据无效，跳过开关状态更新 = {"reason":"undefined值"}
07/23 23:58:22 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":15,"cacheAge":2,"reason":"防止重复调用PositionAnalyticsService"}
07/23 23:58:22 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🧠 智能止损分析完成 = {"决策行动":"继续持有","置信度":"90.00%","风险评分":"14.00","紧急程度":"低","分析原因":["位置安全: 活跃bin位置98.5%，高于30%安全线"]}
07/23 23:58:22 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📡 智能止损数据已广播到前端 = {"instanceId":"chain_position_1753284004910_om4wiz","action":"HOLD","confidence":90}
07/23 23:58:22 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ 策略继续运行 = {"置信度":"90.00%","风险评分":"14.00","策略状态":"正常运行中"}
07/23 23:58:22 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-end = └──────────────────────────────────────────────────────────┘
07/23 23:58:22 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-space = 
07/23 23:58:28 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-start = ┌──────────────────────────────────────────────────────────┐
07/23 23:58:28 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-title = │              连锁头寸策略监控 [0_om4wiz] - 23:58:28              │
07/23 23:58:28 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-separator = ├──────────────────────────────────────────────────────────┤
07/23 23:58:28 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔄 新轮询周期已启动 = {"pollingCycle":16,"pollingInterval":12000,"monitoringInterval":12}
07/23 23:58:29 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 开始调用PositionAnalyticsService获取分析数据 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2,"cycleId":16}
07/23 23:58:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ PositionAnalyticsService数据获取成功 = {"currentPrice":0.00000387858603370761,"positionValue":7.9999786967146225,"netPnL":0.0014664569339277733,"netPnLPercentage":0.018330711674097167,"activeBin":-558,"positionLowerBin":-693,"positionUpperBin":-556,"holdingDuration":0.04991638888888889}
07/23 23:58:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取开始 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/23 23:58:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/23 23:58:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 收益提取条件检查 = {"currentPendingYield":"0.0014877602193047851","yieldExtractionThreshold":"0.02","canExtract":false,"activeBin":-558,"positionLowerBin":-693,"positionUpperBin":-556}
07/23 23:58:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取完成 = {"currentPendingYield":"0.0014877602193047851","totalExtractedYield":"0"}
07/23 23:58:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/23 23:58:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🏗️ 头寸重建决策结果 = {"shouldRecreate":false,"reason":"No recreation needed (智能选择: 默认状态)","recreationType":"NONE","confidence":0,"urgency":"low","recommendedAction":"Continue monitoring","hasOutOfRangeDetails":false,"timeRemaining":null}
07/23 23:58:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🎯 智能状态选择详情 = {"selectedReason":"No recreation needed (智能选择: 默认状态)","stateType":"NONE","confidence":0}
07/23 23:58:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"condition":true}
07/23 23:58:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"isThresholdValid":true,"currentSwitchEnabled":false,"lastSwitchUpdateTime":"2025-07-23T15:55:04.891Z"}
07/23 23:58:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":16,"cacheAge":1,"reason":"防止重复调用PositionAnalyticsService"}
07/23 23:58:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 动态重建开关数据分析 = {"threshold":2,"isDataValid":false,"comparisonResult":"数据无效","willUpdateSwitch":false}
07/23 23:58:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 进入updateDynamicRecreationSwitch方法 = {"instanceId":"chain_position_1753284004910_om4wiz","configThreshold":2,"currentSwitchState":false}
07/23 23:58:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ⚠️ 基准收益率数据无效，跳过开关状态更新 = {"reason":"undefined值"}
07/23 23:58:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":16,"cacheAge":2,"reason":"防止重复调用PositionAnalyticsService"}
07/23 23:58:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🧠 智能止损分析完成 = {"决策行动":"继续持有","置信度":"90.00%","风险评分":"14.00","紧急程度":"低","分析原因":["位置安全: 活跃bin位置98.5%，高于30%安全线"]}
07/23 23:58:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📡 智能止损数据已广播到前端 = {"instanceId":"chain_position_1753284004910_om4wiz","action":"HOLD","confidence":90}
07/23 23:58:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ 策略继续运行 = {"置信度":"90.00%","风险评分":"14.00","策略状态":"正常运行中"}
07/23 23:58:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-end = └──────────────────────────────────────────────────────────┘
07/23 23:58:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-space = 
07/23 23:58:40 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-start = ┌──────────────────────────────────────────────────────────┐
07/23 23:58:40 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-title = │              连锁头寸策略监控 [0_om4wiz] - 23:58:40              │
07/23 23:58:40 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-separator = ├──────────────────────────────────────────────────────────┤
07/23 23:58:40 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔄 新轮询周期已启动 = {"pollingCycle":17,"pollingInterval":12000,"monitoringInterval":12}
07/23 23:58:41 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 开始调用PositionAnalyticsService获取分析数据 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2,"cycleId":17}
07/23 23:58:45 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ PositionAnalyticsService数据获取成功 = {"currentPrice":0.0000038021625661284286,"positionValue":7.999874942388915,"netPnL":0.001659918290560114,"netPnLPercentage":0.020748978632001425,"activeBin":-560,"positionLowerBin":-693,"positionUpperBin":-556,"holdingDuration":0.053553611111111114}
07/23 23:58:45 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取开始 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/23 23:58:45 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/23 23:58:45 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 收益提取条件检查 = {"currentPendingYield":"0.0017849759016449968","yieldExtractionThreshold":"0.02","canExtract":false,"activeBin":-560,"positionLowerBin":-693,"positionUpperBin":-556}
07/23 23:58:45 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取完成 = {"currentPendingYield":"0.0017849759016449968","totalExtractedYield":"0"}
07/23 23:58:45 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/23 23:58:45 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🏗️ 头寸重建决策结果 = {"shouldRecreate":false,"reason":"No recreation needed (智能选择: 默认状态)","recreationType":"NONE","confidence":0,"urgency":"low","recommendedAction":"Continue monitoring","hasOutOfRangeDetails":false,"timeRemaining":null}
07/23 23:58:45 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🎯 智能状态选择详情 = {"selectedReason":"No recreation needed (智能选择: 默认状态)","stateType":"NONE","confidence":0}
07/23 23:58:45 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"condition":true}
07/23 23:58:45 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"isThresholdValid":true,"currentSwitchEnabled":false,"lastSwitchUpdateTime":"2025-07-23T15:55:04.891Z"}
07/23 23:58:45 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":17,"cacheAge":2,"reason":"防止重复调用PositionAnalyticsService"}
07/23 23:58:45 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 动态重建开关数据分析 = {"threshold":2,"isDataValid":false,"comparisonResult":"数据无效","willUpdateSwitch":false}
07/23 23:58:45 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 进入updateDynamicRecreationSwitch方法 = {"instanceId":"chain_position_1753284004910_om4wiz","configThreshold":2,"currentSwitchState":false}
07/23 23:58:45 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ⚠️ 基准收益率数据无效，跳过开关状态更新 = {"reason":"undefined值"}
07/23 23:58:45 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":17,"cacheAge":3,"reason":"防止重复调用PositionAnalyticsService"}
07/23 23:58:45 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🧠 智能止损分析完成 = {"决策行动":"继续持有","置信度":"90.00%","风险评分":"12.00","紧急程度":"低","分析原因":["位置安全: 活跃bin位置97.1%，高于30%安全线"]}
07/23 23:58:45 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📡 智能止损数据已广播到前端 = {"instanceId":"chain_position_1753284004910_om4wiz","action":"HOLD","confidence":90}
07/23 23:58:45 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ 策略继续运行 = {"置信度":"90.00%","风险评分":"12.00","策略状态":"正常运行中"}
07/23 23:58:45 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-end = └──────────────────────────────────────────────────────────┘
07/23 23:58:45 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-space = 
07/23 23:58:52 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-start = ┌──────────────────────────────────────────────────────────┐
07/23 23:58:52 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-title = │              连锁头寸策略监控 [0_om4wiz] - 23:58:52              │
07/23 23:58:52 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-separator = ├──────────────────────────────────────────────────────────┤
07/23 23:58:52 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔄 新轮询周期已启动 = {"pollingCycle":18,"pollingInterval":12000,"monitoringInterval":12}
07/23 23:58:53 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 开始调用PositionAnalyticsService获取分析数据 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2,"cycleId":18}
07/23 23:58:57 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ PositionAnalyticsService数据获取成功 = {"currentPrice":0.0000038021625661284286,"positionValue":7.999874942388915,"netPnL":0.001659918290560114,"netPnLPercentage":0.020748978632001425,"activeBin":-560,"positionLowerBin":-693,"positionUpperBin":-556,"holdingDuration":0.05688861111111111}
07/23 23:58:57 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取开始 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/23 23:58:57 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/23 23:58:57 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 收益提取条件检查 = {"currentPendingYield":"0.0017849759016449968","yieldExtractionThreshold":"0.02","canExtract":false,"activeBin":-560,"positionLowerBin":-693,"positionUpperBin":-556}
07/23 23:58:57 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取完成 = {"currentPendingYield":"0.0017849759016449968","totalExtractedYield":"0"}
07/23 23:58:57 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/23 23:58:57 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🏗️ 头寸重建决策结果 = {"shouldRecreate":false,"reason":"No recreation needed (智能选择: 默认状态)","recreationType":"NONE","confidence":0,"urgency":"low","recommendedAction":"Continue monitoring","hasOutOfRangeDetails":false,"timeRemaining":null}
07/23 23:58:57 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🎯 智能状态选择详情 = {"selectedReason":"No recreation needed (智能选择: 默认状态)","stateType":"NONE","confidence":0}
07/23 23:58:57 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"condition":true}
07/23 23:58:57 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"isThresholdValid":true,"currentSwitchEnabled":false,"lastSwitchUpdateTime":"2025-07-23T15:55:04.891Z"}
07/23 23:58:57 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":18,"cacheAge":1,"reason":"防止重复调用PositionAnalyticsService"}
07/23 23:58:57 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 动态重建开关数据分析 = {"threshold":2,"isDataValid":false,"comparisonResult":"数据无效","willUpdateSwitch":false}
07/23 23:58:57 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 进入updateDynamicRecreationSwitch方法 = {"instanceId":"chain_position_1753284004910_om4wiz","configThreshold":2,"currentSwitchState":false}
07/23 23:58:57 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ⚠️ 基准收益率数据无效，跳过开关状态更新 = {"reason":"undefined值"}
07/23 23:58:57 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":18,"cacheAge":2,"reason":"防止重复调用PositionAnalyticsService"}
07/23 23:58:57 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🧠 智能止损分析完成 = {"决策行动":"继续持有","置信度":"90.00%","风险评分":"13.97","紧急程度":"低","分析原因":["位置安全: 活跃bin位置97.1%，高于30%安全线"]}
07/23 23:58:57 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📡 智能止损数据已广播到前端 = {"instanceId":"chain_position_1753284004910_om4wiz","action":"HOLD","confidence":90}
07/23 23:58:57 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ 策略继续运行 = {"置信度":"90.00%","风险评分":"13.97","策略状态":"正常运行中"}
07/23 23:58:57 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-end = └──────────────────────────────────────────────────────────┘
07/23 23:58:57 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-space = 
07/23 23:59:04 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-start = ┌──────────────────────────────────────────────────────────┐
07/23 23:59:04 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-title = │              连锁头寸策略监控 [0_om4wiz] - 23:59:04              │
07/23 23:59:04 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-separator = ├──────────────────────────────────────────────────────────┤
07/23 23:59:04 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔄 新轮询周期已启动 = {"pollingCycle":19,"pollingInterval":12000,"monitoringInterval":12}
07/23 23:59:05 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 开始调用PositionAnalyticsService获取分析数据 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2,"cycleId":19}
07/23 23:59:09 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ PositionAnalyticsService数据获取成功 = {"currentPrice":0.0000038021625661284286,"positionValue":7.999874942388915,"netPnL":0.001659918290560114,"netPnLPercentage":0.020748978632001425,"activeBin":-560,"positionLowerBin":-693,"positionUpperBin":-556,"holdingDuration":0.060269722222222225}
07/23 23:59:09 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取开始 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/23 23:59:09 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/23 23:59:09 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 收益提取条件检查 = {"currentPendingYield":"0.0017849759016449968","yieldExtractionThreshold":"0.02","canExtract":false,"activeBin":-560,"positionLowerBin":-693,"positionUpperBin":-556}
07/23 23:59:09 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取完成 = {"currentPendingYield":"0.0017849759016449968","totalExtractedYield":"0"}
07/23 23:59:09 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/23 23:59:09 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🏗️ 头寸重建决策结果 = {"shouldRecreate":false,"reason":"No recreation needed (智能选择: 默认状态)","recreationType":"NONE","confidence":0,"urgency":"low","recommendedAction":"Continue monitoring","hasOutOfRangeDetails":false,"timeRemaining":null}
07/23 23:59:09 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🎯 智能状态选择详情 = {"selectedReason":"No recreation needed (智能选择: 默认状态)","stateType":"NONE","confidence":0}
07/23 23:59:09 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"condition":true}
07/23 23:59:09 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"isThresholdValid":true,"currentSwitchEnabled":false,"lastSwitchUpdateTime":"2025-07-23T15:55:04.891Z"}
07/23 23:59:09 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":19,"cacheAge":1,"reason":"防止重复调用PositionAnalyticsService"}
07/23 23:59:09 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 动态重建开关数据分析 = {"threshold":2,"isDataValid":false,"comparisonResult":"数据无效","willUpdateSwitch":false}
07/23 23:59:09 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 进入updateDynamicRecreationSwitch方法 = {"instanceId":"chain_position_1753284004910_om4wiz","configThreshold":2,"currentSwitchState":false}
07/23 23:59:09 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ⚠️ 基准收益率数据无效，跳过开关状态更新 = {"reason":"undefined值"}
07/23 23:59:09 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":19,"cacheAge":1,"reason":"防止重复调用PositionAnalyticsService"}
07/23 23:59:09 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🧠 智能止损分析完成 = {"决策行动":"继续持有","置信度":"90.00%","风险评分":"12.00","紧急程度":"低","分析原因":["位置安全: 活跃bin位置97.1%，高于30%安全线"]}
07/23 23:59:09 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📡 智能止损数据已广播到前端 = {"instanceId":"chain_position_1753284004910_om4wiz","action":"HOLD","confidence":90}
07/23 23:59:09 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ 策略继续运行 = {"置信度":"90.00%","风险评分":"12.00","策略状态":"正常运行中"}
07/23 23:59:09 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-end = └──────────────────────────────────────────────────────────┘
07/23 23:59:09 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-space = 
07/23 23:59:16 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-start = ┌──────────────────────────────────────────────────────────┐
07/23 23:59:16 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-title = │              连锁头寸策略监控 [0_om4wiz] - 23:59:16              │
07/23 23:59:16 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-separator = ├──────────────────────────────────────────────────────────┤
07/23 23:59:16 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔄 新轮询周期已启动 = {"pollingCycle":20,"pollingInterval":12000,"monitoringInterval":12}
07/23 23:59:17 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 开始调用PositionAnalyticsService获取分析数据 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2,"cycleId":20}
07/23 23:59:20 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ PositionAnalyticsService数据获取成功 = {"currentPrice":0.0000038021625661284286,"positionValue":7.999874942388915,"netPnL":0.001659918290560114,"netPnLPercentage":0.020748978632001425,"activeBin":-560,"positionLowerBin":-693,"positionUpperBin":-556,"holdingDuration":0.06324166666666667}
07/23 23:59:20 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取开始 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/23 23:59:20 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/23 23:59:22 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 收益提取条件检查 = {"currentPendingYield":"0.0017849759016449968","yieldExtractionThreshold":"0.02","canExtract":false,"activeBin":-560,"positionLowerBin":-693,"positionUpperBin":-556}
07/23 23:59:22 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取完成 = {"currentPendingYield":"0.0017849759016449968","totalExtractedYield":"0"}
07/23 23:59:22 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/23 23:59:22 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🏗️ 头寸重建决策结果 = {"shouldRecreate":false,"reason":"No recreation needed (智能选择: 默认状态)","recreationType":"NONE","confidence":0,"urgency":"low","recommendedAction":"Continue monitoring","hasOutOfRangeDetails":false,"timeRemaining":null}
07/23 23:59:22 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🎯 智能状态选择详情 = {"selectedReason":"No recreation needed (智能选择: 默认状态)","stateType":"NONE","confidence":0}
07/23 23:59:22 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"condition":true}
07/23 23:59:22 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"isThresholdValid":true,"currentSwitchEnabled":false,"lastSwitchUpdateTime":"2025-07-23T15:55:04.891Z"}
07/23 23:59:22 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":20,"cacheAge":1,"reason":"防止重复调用PositionAnalyticsService"}
07/23 23:59:22 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 动态重建开关数据分析 = {"threshold":2,"isDataValid":false,"comparisonResult":"数据无效","willUpdateSwitch":false}
07/23 23:59:22 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 进入updateDynamicRecreationSwitch方法 = {"instanceId":"chain_position_1753284004910_om4wiz","configThreshold":2,"currentSwitchState":false}
07/23 23:59:22 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ⚠️ 基准收益率数据无效，跳过开关状态更新 = {"reason":"undefined值"}
07/23 23:59:22 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":20,"cacheAge":2,"reason":"防止重复调用PositionAnalyticsService"}
07/23 23:59:22 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🧠 智能止损分析完成 = {"决策行动":"继续持有","置信度":"90.00%","风险评分":"12.00","紧急程度":"低","分析原因":["位置安全: 活跃bin位置97.1%，高于30%安全线"]}
07/23 23:59:22 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📡 智能止损数据已广播到前端 = {"instanceId":"chain_position_1753284004910_om4wiz","action":"HOLD","confidence":90}
07/23 23:59:22 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ 策略继续运行 = {"置信度":"90.00%","风险评分":"12.00","策略状态":"正常运行中"}
07/23 23:59:22 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-end = └──────────────────────────────────────────────────────────┘
07/23 23:59:22 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-space = 
07/23 23:59:28 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-start = ┌──────────────────────────────────────────────────────────┐
07/23 23:59:28 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-title = │              连锁头寸策略监控 [0_om4wiz] - 23:59:28              │
07/23 23:59:28 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-separator = ├──────────────────────────────────────────────────────────┤
07/23 23:59:28 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔄 新轮询周期已启动 = {"pollingCycle":21,"pollingInterval":12000,"monitoringInterval":12}
07/23 23:59:29 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 开始调用PositionAnalyticsService获取分析数据 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2,"cycleId":21}
07/23 23:59:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ PositionAnalyticsService数据获取成功 = {"currentPrice":0.0000034764686524454443,"positionValue":7.999034929164282,"netPnL":0.00445898525996391,"netPnLPercentage":0.05573731574954888,"activeBin":-569,"positionLowerBin":-693,"positionUpperBin":-556,"holdingDuration":0.06645722222222222}
07/23 23:59:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取开始 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/23 23:59:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/23 23:59:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 收益提取条件检查 = {"currentPendingYield":"0.005424056095680956","yieldExtractionThreshold":"0.02","canExtract":false,"activeBin":-569,"positionLowerBin":-693,"positionUpperBin":-556}
07/23 23:59:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取完成 = {"currentPendingYield":"0.005424056095680956","totalExtractedYield":"0"}
07/23 23:59:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/23 23:59:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🏗️ 头寸重建决策结果 = {"shouldRecreate":false,"reason":"No recreation needed (智能选择: 默认状态)","recreationType":"NONE","confidence":0,"urgency":"low","recommendedAction":"Continue monitoring","hasOutOfRangeDetails":false,"timeRemaining":null}
07/23 23:59:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🎯 智能状态选择详情 = {"selectedReason":"No recreation needed (智能选择: 默认状态)","stateType":"NONE","confidence":0}
07/23 23:59:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"condition":true}
07/23 23:59:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"isThresholdValid":true,"currentSwitchEnabled":false,"lastSwitchUpdateTime":"2025-07-23T15:55:04.891Z"}
07/23 23:59:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":21,"cacheAge":2,"reason":"防止重复调用PositionAnalyticsService"}
07/23 23:59:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 动态重建开关数据分析 = {"threshold":2,"isDataValid":false,"comparisonResult":"数据无效","willUpdateSwitch":false}
07/23 23:59:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 进入updateDynamicRecreationSwitch方法 = {"instanceId":"chain_position_1753284004910_om4wiz","configThreshold":2,"currentSwitchState":false}
07/23 23:59:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ⚠️ 基准收益率数据无效，跳过开关状态更新 = {"reason":"undefined值"}
07/23 23:59:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":21,"cacheAge":3,"reason":"防止重复调用PositionAnalyticsService"}
07/23 23:59:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🧠 智能止损分析完成 = {"决策行动":"继续持有","置信度":"90.00%","风险评分":"12.00","紧急程度":"低","分析原因":["位置安全: 活跃bin位置90.5%，高于30%安全线"]}
07/23 23:59:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📡 智能止损数据已广播到前端 = {"instanceId":"chain_position_1753284004910_om4wiz","action":"HOLD","confidence":90}
07/23 23:59:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ 策略继续运行 = {"置信度":"90.00%","风险评分":"12.00","策略状态":"正常运行中"}
07/23 23:59:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-end = └──────────────────────────────────────────────────────────┘
07/23 23:59:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-space = 
07/23 23:59:40 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-start = ┌──────────────────────────────────────────────────────────┐
07/23 23:59:40 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-title = │              连锁头寸策略监控 [0_om4wiz] - 23:59:40              │
07/23 23:59:40 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-separator = ├──────────────────────────────────────────────────────────┤
07/23 23:59:40 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔄 新轮询周期已启动 = {"pollingCycle":22,"pollingInterval":12000,"monitoringInterval":12}
07/23 23:59:41 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 开始调用PositionAnalyticsService获取分析数据 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2,"cycleId":22}
07/23 23:59:45 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ PositionAnalyticsService数据获取成功 = {"currentPrice":0.0000034764686524454443,"positionValue":7.997107021056605,"netPnL":0.0025310771522857323,"netPnLPercentage":0.03163846440357165,"activeBin":-569,"positionLowerBin":-693,"positionUpperBin":-556,"holdingDuration":0.07026166666666667}
07/23 23:59:45 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取开始 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/23 23:59:45 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/23 23:59:45 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 收益提取条件检查 = {"currentPendingYield":"0.005424056095680956","yieldExtractionThreshold":"0.02","canExtract":false,"activeBin":-569,"positionLowerBin":-693,"positionUpperBin":-556}
07/23 23:59:45 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取完成 = {"currentPendingYield":"0.005424056095680956","totalExtractedYield":"0"}
07/23 23:59:45 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/23 23:59:45 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🏗️ 头寸重建决策结果 = {"shouldRecreate":false,"reason":"No recreation needed (智能选择: 默认状态)","recreationType":"NONE","confidence":0,"urgency":"low","recommendedAction":"Continue monitoring","hasOutOfRangeDetails":false,"timeRemaining":null}
07/23 23:59:45 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🎯 智能状态选择详情 = {"selectedReason":"No recreation needed (智能选择: 默认状态)","stateType":"NONE","confidence":0}
07/23 23:59:45 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"condition":true}
07/23 23:59:45 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"isThresholdValid":true,"currentSwitchEnabled":false,"lastSwitchUpdateTime":"2025-07-23T15:55:04.891Z"}
07/23 23:59:45 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":22,"cacheAge":1,"reason":"防止重复调用PositionAnalyticsService"}
07/23 23:59:45 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 动态重建开关数据分析 = {"threshold":2,"isDataValid":false,"comparisonResult":"数据无效","willUpdateSwitch":false}
07/23 23:59:45 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 进入updateDynamicRecreationSwitch方法 = {"instanceId":"chain_position_1753284004910_om4wiz","configThreshold":2,"currentSwitchState":false}
07/23 23:59:45 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ⚠️ 基准收益率数据无效，跳过开关状态更新 = {"reason":"undefined值"}
07/23 23:59:45 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":22,"cacheAge":2,"reason":"防止重复调用PositionAnalyticsService"}
07/23 23:59:45 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🧠 智能止损分析完成 = {"决策行动":"继续持有","置信度":"90.00%","风险评分":"20.57","紧急程度":"低","分析原因":["位置安全: 活跃bin位置90.5%，高于30%安全线"]}
07/23 23:59:45 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📡 智能止损数据已广播到前端 = {"instanceId":"chain_position_1753284004910_om4wiz","action":"HOLD","confidence":90}
07/23 23:59:45 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ 策略继续运行 = {"置信度":"90.00%","风险评分":"20.57","策略状态":"正常运行中"}
07/23 23:59:45 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-end = └──────────────────────────────────────────────────────────┘
07/23 23:59:45 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-space = 
07/23 23:59:52 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-start = ┌──────────────────────────────────────────────────────────┐
07/23 23:59:52 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-title = │              连锁头寸策略监控 [0_om4wiz] - 23:59:52              │
07/23 23:59:52 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-separator = ├──────────────────────────────────────────────────────────┤
07/23 23:59:52 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔄 新轮询周期已启动 = {"pollingCycle":23,"pollingInterval":12000,"monitoringInterval":12}
07/23 23:59:53 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 开始调用PositionAnalyticsService获取分析数据 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2,"cycleId":23}
07/23 23:59:59 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ PositionAnalyticsService数据获取成功 = {"currentPrice":0.000003511233338969899,"positionValue":7.99768066748338,"netPnL":0.0033851231400170434,"netPnLPercentage":0.04231403925021304,"activeBin":-568,"positionLowerBin":-693,"positionUpperBin":-556,"holdingDuration":0.07407416666666666}
07/23 23:59:59 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取开始 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/23 23:59:59 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/23 23:59:59 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 收益提取条件检查 = {"currentPendingYield":"0.005704455656637766","yieldExtractionThreshold":"0.02","canExtract":false,"activeBin":-568,"positionLowerBin":-693,"positionUpperBin":-556}
07/23 23:59:59 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取完成 = {"currentPendingYield":"0.005704455656637766","totalExtractedYield":"0"}
07/23 23:59:59 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/23 23:59:59 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🏗️ 头寸重建决策结果 = {"shouldRecreate":false,"reason":"No recreation needed (智能选择: 默认状态)","recreationType":"NONE","confidence":0,"urgency":"low","recommendedAction":"Continue monitoring","hasOutOfRangeDetails":false,"timeRemaining":null}
07/23 23:59:59 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🎯 智能状态选择详情 = {"selectedReason":"No recreation needed (智能选择: 默认状态)","stateType":"NONE","confidence":0}
07/23 23:59:59 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"condition":true}
07/23 23:59:59 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"isThresholdValid":true,"currentSwitchEnabled":false,"lastSwitchUpdateTime":"2025-07-23T15:55:04.891Z"}
07/23 23:59:59 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":23,"cacheAge":1,"reason":"防止重复调用PositionAnalyticsService"}
07/23 23:59:59 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 动态重建开关数据分析 = {"threshold":2,"isDataValid":false,"comparisonResult":"数据无效","willUpdateSwitch":false}
07/23 23:59:59 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 进入updateDynamicRecreationSwitch方法 = {"instanceId":"chain_position_1753284004910_om4wiz","configThreshold":2,"currentSwitchState":false}
07/23 23:59:59 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ⚠️ 基准收益率数据无效，跳过开关状态更新 = {"reason":"undefined值"}
07/23 23:59:59 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":23,"cacheAge":2,"reason":"防止重复调用PositionAnalyticsService"}
07/23 23:59:59 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🧠 智能止损分析完成 = {"决策行动":"继续持有","置信度":"90.00%","风险评分":"12.00","紧急程度":"低","分析原因":["位置安全: 活跃bin位置91.2%，高于30%安全线"]}
07/23 23:59:59 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📡 智能止损数据已广播到前端 = {"instanceId":"chain_position_1753284004910_om4wiz","action":"HOLD","confidence":90}
07/23 23:59:59 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ 策略继续运行 = {"置信度":"90.00%","风险评分":"12.00","策略状态":"正常运行中"}
07/23 23:59:59 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-end = └──────────────────────────────────────────────────────────┘
07/23 23:59:59 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-space = 
07/24 00:00:04 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-start = ┌──────────────────────────────────────────────────────────┐
07/24 00:00:04 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-title = │              连锁头寸策略监控 [0_om4wiz] - 00:00:04              │
07/24 00:00:04 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-separator = ├──────────────────────────────────────────────────────────┤
07/24 00:00:04 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔄 新轮询周期已启动 = {"pollingCycle":24,"pollingInterval":12000,"monitoringInterval":12}
07/24 00:00:05 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 开始调用PositionAnalyticsService获取分析数据 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2,"cycleId":24}
07/24 00:00:08 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ PositionAnalyticsService数据获取成功 = {"currentPrice":0.000003511233338969899,"positionValue":7.997680785625848,"netPnL":0.003385241282485296,"netPnLPercentage":0.0423155160310662,"activeBin":-568,"positionLowerBin":-693,"positionUpperBin":-556,"holdingDuration":0.076615}
07/24 00:00:08 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取开始 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:00:08 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:00:08 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 收益提取条件检查 = {"currentPendingYield":"0.005704455656637766","yieldExtractionThreshold":"0.02","canExtract":false,"activeBin":-568,"positionLowerBin":-693,"positionUpperBin":-556}
07/24 00:00:08 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取完成 = {"currentPendingYield":"0.005704455656637766","totalExtractedYield":"0"}
07/24 00:00:08 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:00:08 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🏗️ 头寸重建决策结果 = {"shouldRecreate":false,"reason":"No recreation needed (智能选择: 默认状态)","recreationType":"NONE","confidence":0,"urgency":"low","recommendedAction":"Continue monitoring","hasOutOfRangeDetails":false,"timeRemaining":null}
07/24 00:00:08 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🎯 智能状态选择详情 = {"selectedReason":"No recreation needed (智能选择: 默认状态)","stateType":"NONE","confidence":0}
07/24 00:00:08 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"condition":true}
07/24 00:00:08 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"isThresholdValid":true,"currentSwitchEnabled":false,"lastSwitchUpdateTime":"2025-07-23T15:55:04.891Z"}
07/24 00:00:08 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":24,"cacheAge":1,"reason":"防止重复调用PositionAnalyticsService"}
07/24 00:00:08 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 动态重建开关数据分析 = {"threshold":2,"isDataValid":false,"comparisonResult":"数据无效","willUpdateSwitch":false}
07/24 00:00:08 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 进入updateDynamicRecreationSwitch方法 = {"instanceId":"chain_position_1753284004910_om4wiz","configThreshold":2,"currentSwitchState":false}
07/24 00:00:08 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ⚠️ 基准收益率数据无效，跳过开关状态更新 = {"reason":"undefined值"}
07/24 00:00:08 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":24,"cacheAge":2,"reason":"防止重复调用PositionAnalyticsService"}
07/24 00:00:08 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🧠 智能止损分析完成 = {"决策行动":"继续持有","置信度":"90.00%","风险评分":"13.00","紧急程度":"低","分析原因":["位置安全: 活跃bin位置91.2%，高于30%安全线"]}
07/24 00:00:08 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📡 智能止损数据已广播到前端 = {"instanceId":"chain_position_1753284004910_om4wiz","action":"HOLD","confidence":90}
07/24 00:00:08 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ 策略继续运行 = {"置信度":"90.00%","风险评分":"13.00","策略状态":"正常运行中"}
07/24 00:00:08 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-end = └──────────────────────────────────────────────────────────┘
07/24 00:00:08 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-space = 
07/24 00:00:16 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-start = ┌──────────────────────────────────────────────────────────┐
07/24 00:00:16 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-title = │              连锁头寸策略监控 [0_om4wiz] - 00:00:16              │
07/24 00:00:16 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-separator = ├──────────────────────────────────────────────────────────┤
07/24 00:00:16 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔄 新轮询周期已启动 = {"pollingCycle":25,"pollingInterval":12000,"monitoringInterval":12}
07/24 00:00:17 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 开始调用PositionAnalyticsService获取分析数据 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2,"cycleId":25}
07/24 00:00:22 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ PositionAnalyticsService数据获取成功 = {"currentPrice":0.000003511233338969899,"positionValue":7.997681620910042,"netPnL":0.0037790065666793993,"netPnLPercentage":0.04723758208349249,"activeBin":-568,"positionLowerBin":-693,"positionUpperBin":-556,"holdingDuration":0.08056555555555556}
07/24 00:00:22 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取开始 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:00:22 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:00:22 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 收益提取条件检查 = {"currentPendingYield":"0.006097385656637765","yieldExtractionThreshold":"0.02","canExtract":false,"activeBin":-568,"positionLowerBin":-693,"positionUpperBin":-556}
07/24 00:00:23 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取完成 = {"currentPendingYield":"0.006097385656637765","totalExtractedYield":"0"}
07/24 00:00:23 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:00:23 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🏗️ 头寸重建决策结果 = {"shouldRecreate":false,"reason":"No recreation needed (智能选择: 默认状态)","recreationType":"NONE","confidence":0,"urgency":"low","recommendedAction":"Continue monitoring","hasOutOfRangeDetails":false,"timeRemaining":null}
07/24 00:00:23 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🎯 智能状态选择详情 = {"selectedReason":"No recreation needed (智能选择: 默认状态)","stateType":"NONE","confidence":0}
07/24 00:00:23 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"condition":true}
07/24 00:00:23 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"isThresholdValid":true,"currentSwitchEnabled":false,"lastSwitchUpdateTime":"2025-07-23T15:55:04.891Z"}
07/24 00:00:23 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":25,"cacheAge":1,"reason":"防止重复调用PositionAnalyticsService"}
07/24 00:00:23 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 动态重建开关数据分析 = {"benchmarkYield5Min":null,"threshold":2,"isDataValid":false,"comparisonResult":"数据无效","willUpdateSwitch":false}
07/24 00:00:23 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 进入updateDynamicRecreationSwitch方法 = {"instanceId":"chain_position_1753284004910_om4wiz","benchmarkYield5Min":null,"configThreshold":2,"currentSwitchState":false}
07/24 00:00:23 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ⚠️ 基准收益率数据无效，跳过开关状态更新 = {"benchmarkYield5Min":null,"reason":"null值"}
07/24 00:00:23 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":25,"cacheAge":2,"reason":"防止重复调用PositionAnalyticsService"}
07/24 00:00:23 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🧠 智能止损分析完成 = {"决策行动":"继续持有","置信度":"90.00%","风险评分":"12.00","紧急程度":"低","分析原因":["位置安全: 活跃bin位置91.2%，高于30%安全线"]}
07/24 00:00:23 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📡 智能止损数据已广播到前端 = {"instanceId":"chain_position_1753284004910_om4wiz","action":"HOLD","confidence":90}
07/24 00:00:23 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ 策略继续运行 = {"置信度":"90.00%","风险评分":"12.00","策略状态":"正常运行中"}
07/24 00:00:23 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-end = └──────────────────────────────────────────────────────────┘
07/24 00:00:23 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-space = 
07/24 00:00:28 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-start = ┌──────────────────────────────────────────────────────────┐
07/24 00:00:28 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-title = │              连锁头寸策略监控 [0_om4wiz] - 00:00:28              │
07/24 00:00:28 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-separator = ├──────────────────────────────────────────────────────────┤
07/24 00:00:28 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔄 新轮询周期已启动 = {"pollingCycle":26,"pollingInterval":12000,"monitoringInterval":12}
07/24 00:00:29 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 开始调用PositionAnalyticsService获取分析数据 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2,"cycleId":26}
07/24 00:00:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ PositionAnalyticsService数据获取成功 = {"currentPrice":0.000003511233338969899,"positionValue":7.997683285680366,"netPnL":0.0037806713370045486,"netPnLPercentage":0.04725839171255686,"activeBin":-568,"positionLowerBin":-693,"positionUpperBin":-556,"holdingDuration":0.08327333333333334}
07/24 00:00:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取开始 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:00:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:00:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 收益提取条件检查 = {"currentPendingYield":"0.006097385656637765","yieldExtractionThreshold":"0.02","canExtract":false,"activeBin":-568,"positionLowerBin":-693,"positionUpperBin":-556}
07/24 00:00:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取完成 = {"currentPendingYield":"0.006097385656637765","totalExtractedYield":"0"}
07/24 00:00:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:00:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🏗️ 头寸重建决策结果 = {"shouldRecreate":false,"reason":"No recreation needed (智能选择: 默认状态)","recreationType":"NONE","confidence":0,"urgency":"low","recommendedAction":"Continue monitoring","hasOutOfRangeDetails":false,"timeRemaining":null}
07/24 00:00:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🎯 智能状态选择详情 = {"selectedReason":"No recreation needed (智能选择: 默认状态)","stateType":"NONE","confidence":0}
07/24 00:00:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"condition":true}
07/24 00:00:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"isThresholdValid":true,"currentSwitchEnabled":false,"lastSwitchUpdateTime":"2025-07-23T15:55:04.891Z"}
07/24 00:00:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":26,"cacheAge":1,"reason":"防止重复调用PositionAnalyticsService"}
07/24 00:00:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 动态重建开关数据分析 = {"benchmarkYield5Min":null,"threshold":2,"isDataValid":false,"comparisonResult":"数据无效","willUpdateSwitch":false}
07/24 00:00:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 进入updateDynamicRecreationSwitch方法 = {"instanceId":"chain_position_1753284004910_om4wiz","benchmarkYield5Min":null,"configThreshold":2,"currentSwitchState":false}
07/24 00:00:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ⚠️ 基准收益率数据无效，跳过开关状态更新 = {"benchmarkYield5Min":null,"reason":"null值"}
07/24 00:00:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":26,"cacheAge":2,"reason":"防止重复调用PositionAnalyticsService"}
07/24 00:00:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🧠 智能止损分析完成 = {"决策行动":"继续持有","置信度":"90.00%","风险评分":"12.00","紧急程度":"低","分析原因":["位置安全: 活跃bin位置91.2%，高于30%安全线"]}
07/24 00:00:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📡 智能止损数据已广播到前端 = {"instanceId":"chain_position_1753284004910_om4wiz","action":"HOLD","confidence":90}
07/24 00:00:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ 策略继续运行 = {"置信度":"90.00%","风险评分":"12.00","策略状态":"正常运行中"}
07/24 00:00:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-end = └──────────────────────────────────────────────────────────┘
07/24 00:00:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-space = 
07/24 00:00:40 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-start = ┌──────────────────────────────────────────────────────────┐
07/24 00:00:40 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-title = │              连锁头寸策略监控 [0_om4wiz] - 00:00:40              │
07/24 00:00:40 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-separator = ├──────────────────────────────────────────────────────────┤
07/24 00:00:40 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔄 新轮询周期已启动 = {"pollingCycle":27,"pollingInterval":12000,"monitoringInterval":12}
07/24 00:00:41 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 开始调用PositionAnalyticsService获取分析数据 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2,"cycleId":27}
07/24 00:00:45 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ PositionAnalyticsService数据获取成功 = {"currentPrice":0.000003511233338969899,"positionValue":7.997683285680366,"netPnL":0.0037806713370045486,"netPnLPercentage":0.04725839171255686,"activeBin":-568,"positionLowerBin":-693,"positionUpperBin":-556,"holdingDuration":0.08671305555555556}
07/24 00:00:45 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取开始 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:00:45 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:00:45 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 收益提取条件检查 = {"currentPendingYield":"0.006097385656637765","yieldExtractionThreshold":"0.02","canExtract":false,"activeBin":-568,"positionLowerBin":-693,"positionUpperBin":-556}
07/24 00:00:45 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取完成 = {"currentPendingYield":"0.006097385656637765","totalExtractedYield":"0"}
07/24 00:00:45 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:00:45 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🏗️ 头寸重建决策结果 = {"shouldRecreate":false,"reason":"No recreation needed (智能选择: 默认状态)","recreationType":"NONE","confidence":0,"urgency":"low","recommendedAction":"Continue monitoring","hasOutOfRangeDetails":false,"timeRemaining":null}
07/24 00:00:45 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🎯 智能状态选择详情 = {"selectedReason":"No recreation needed (智能选择: 默认状态)","stateType":"NONE","confidence":0}
07/24 00:00:45 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"condition":true}
07/24 00:00:45 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"isThresholdValid":true,"currentSwitchEnabled":false,"lastSwitchUpdateTime":"2025-07-23T15:55:04.891Z"}
07/24 00:00:45 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":27,"cacheAge":1,"reason":"防止重复调用PositionAnalyticsService"}
07/24 00:00:45 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 动态重建开关数据分析 = {"benchmarkYield5Min":null,"threshold":2,"isDataValid":false,"comparisonResult":"数据无效","willUpdateSwitch":false}
07/24 00:00:45 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 进入updateDynamicRecreationSwitch方法 = {"instanceId":"chain_position_1753284004910_om4wiz","benchmarkYield5Min":null,"configThreshold":2,"currentSwitchState":false}
07/24 00:00:45 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ⚠️ 基准收益率数据无效，跳过开关状态更新 = {"benchmarkYield5Min":null,"reason":"null值"}
07/24 00:00:45 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":27,"cacheAge":2,"reason":"防止重复调用PositionAnalyticsService"}
07/24 00:00:45 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🧠 智能止损分析完成 = {"决策行动":"继续持有","置信度":"90.00%","风险评分":"12.00","紧急程度":"低","分析原因":["位置安全: 活跃bin位置91.2%，高于30%安全线"]}
07/24 00:00:45 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📡 智能止损数据已广播到前端 = {"instanceId":"chain_position_1753284004910_om4wiz","action":"HOLD","confidence":90}
07/24 00:00:45 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ 策略继续运行 = {"置信度":"90.00%","风险评分":"12.00","策略状态":"正常运行中"}
07/24 00:00:45 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-end = └──────────────────────────────────────────────────────────┘
07/24 00:00:45 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-space = 
07/24 00:00:52 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-start = ┌──────────────────────────────────────────────────────────┐
07/24 00:00:52 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-title = │              连锁头寸策略监控 [0_om4wiz] - 00:00:52              │
07/24 00:00:52 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-separator = ├──────────────────────────────────────────────────────────┤
07/24 00:00:52 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔄 新轮询周期已启动 = {"pollingCycle":28,"pollingInterval":12000,"monitoringInterval":12}
07/24 00:00:53 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 开始调用PositionAnalyticsService获取分析数据 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2,"cycleId":28}
07/24 00:00:57 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ PositionAnalyticsService数据获取成功 = {"currentPrice":0.000003511233338969899,"positionValue":7.997683285680366,"netPnL":0.0037806713370045486,"netPnLPercentage":0.04725839171255686,"activeBin":-568,"positionLowerBin":-693,"positionUpperBin":-556,"holdingDuration":0.09012388888888889}
07/24 00:00:57 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取开始 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:00:57 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:00:57 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 收益提取条件检查 = {"currentPendingYield":"0.006097385656637765","yieldExtractionThreshold":"0.02","canExtract":false,"activeBin":-568,"positionLowerBin":-693,"positionUpperBin":-556}
07/24 00:00:57 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取完成 = {"currentPendingYield":"0.006097385656637765","totalExtractedYield":"0"}
07/24 00:00:57 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:00:57 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🏗️ 头寸重建决策结果 = {"shouldRecreate":false,"reason":"No recreation needed (智能选择: 默认状态)","recreationType":"NONE","confidence":0,"urgency":"low","recommendedAction":"Continue monitoring","hasOutOfRangeDetails":false,"timeRemaining":null}
07/24 00:00:57 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🎯 智能状态选择详情 = {"selectedReason":"No recreation needed (智能选择: 默认状态)","stateType":"NONE","confidence":0}
07/24 00:00:57 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"condition":true}
07/24 00:00:57 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"isThresholdValid":true,"currentSwitchEnabled":false,"lastSwitchUpdateTime":"2025-07-23T15:55:04.891Z"}
07/24 00:00:57 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":28,"cacheAge":1,"reason":"防止重复调用PositionAnalyticsService"}
07/24 00:00:57 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 动态重建开关数据分析 = {"benchmarkYield5Min":null,"threshold":2,"isDataValid":false,"comparisonResult":"数据无效","willUpdateSwitch":false}
07/24 00:00:57 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 进入updateDynamicRecreationSwitch方法 = {"instanceId":"chain_position_1753284004910_om4wiz","benchmarkYield5Min":null,"configThreshold":2,"currentSwitchState":false}
07/24 00:00:57 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ⚠️ 基准收益率数据无效，跳过开关状态更新 = {"benchmarkYield5Min":null,"reason":"null值"}
07/24 00:00:57 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":28,"cacheAge":2,"reason":"防止重复调用PositionAnalyticsService"}
07/24 00:00:57 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🧠 智能止损分析完成 = {"决策行动":"继续持有","置信度":"90.00%","风险评分":"12.00","紧急程度":"低","分析原因":["位置安全: 活跃bin位置91.2%，高于30%安全线"]}
07/24 00:00:57 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📡 智能止损数据已广播到前端 = {"instanceId":"chain_position_1753284004910_om4wiz","action":"HOLD","confidence":90}
07/24 00:00:57 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ 策略继续运行 = {"置信度":"90.00%","风险评分":"12.00","策略状态":"正常运行中"}
07/24 00:00:57 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-end = └──────────────────────────────────────────────────────────┘
07/24 00:00:57 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-space = 
07/24 00:01:04 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-start = ┌──────────────────────────────────────────────────────────┐
07/24 00:01:04 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-title = │              连锁头寸策略监控 [0_om4wiz] - 00:01:04              │
07/24 00:01:04 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-separator = ├──────────────────────────────────────────────────────────┤
07/24 00:01:04 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔄 新轮询周期已启动 = {"pollingCycle":29,"pollingInterval":12000,"monitoringInterval":12}
07/24 00:01:05 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 开始调用PositionAnalyticsService获取分析数据 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2,"cycleId":29}
07/24 00:01:10 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ PositionAnalyticsService数据获取成功 = {"currentPrice":0.000003511233338969899,"positionValue":7.997683285680366,"netPnL":0.003971221337003783,"netPnLPercentage":0.04964026671254729,"activeBin":-568,"positionLowerBin":-693,"positionUpperBin":-556,"holdingDuration":0.09376888888888889}
07/24 00:01:10 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取开始 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:01:10 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:01:10 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 收益提取条件检查 = {"currentPendingYield":"0.006287935656637766","yieldExtractionThreshold":"0.02","canExtract":false,"activeBin":-568,"positionLowerBin":-693,"positionUpperBin":-556}
07/24 00:01:10 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取完成 = {"currentPendingYield":"0.006287935656637766","totalExtractedYield":"0"}
07/24 00:01:10 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:01:10 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🏗️ 头寸重建决策结果 = {"shouldRecreate":false,"reason":"No recreation needed (智能选择: 默认状态)","recreationType":"NONE","confidence":0,"urgency":"low","recommendedAction":"Continue monitoring","hasOutOfRangeDetails":false,"timeRemaining":null}
07/24 00:01:10 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🎯 智能状态选择详情 = {"selectedReason":"No recreation needed (智能选择: 默认状态)","stateType":"NONE","confidence":0}
07/24 00:01:10 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"condition":true}
07/24 00:01:10 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"isThresholdValid":true,"currentSwitchEnabled":false,"lastSwitchUpdateTime":"2025-07-23T15:55:04.891Z"}
07/24 00:01:10 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":29,"cacheAge":1,"reason":"防止重复调用PositionAnalyticsService"}
07/24 00:01:10 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 动态重建开关数据分析 = {"benchmarkYield5Min":null,"threshold":2,"isDataValid":false,"comparisonResult":"数据无效","willUpdateSwitch":false}
07/24 00:01:10 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 进入updateDynamicRecreationSwitch方法 = {"instanceId":"chain_position_1753284004910_om4wiz","benchmarkYield5Min":null,"configThreshold":2,"currentSwitchState":false}
07/24 00:01:10 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ⚠️ 基准收益率数据无效，跳过开关状态更新 = {"benchmarkYield5Min":null,"reason":"null值"}
07/24 00:01:10 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":29,"cacheAge":2,"reason":"防止重复调用PositionAnalyticsService"}
07/24 00:01:10 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🧠 智能止损分析完成 = {"决策行动":"继续持有","置信度":"90.00%","风险评分":"12.00","紧急程度":"低","分析原因":["位置安全: 活跃bin位置91.2%，高于30%安全线"]}
07/24 00:01:10 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📡 智能止损数据已广播到前端 = {"instanceId":"chain_position_1753284004910_om4wiz","action":"HOLD","confidence":90}
07/24 00:01:10 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ 策略继续运行 = {"置信度":"90.00%","风险评分":"12.00","策略状态":"正常运行中"}
07/24 00:01:10 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-end = └──────────────────────────────────────────────────────────┘
07/24 00:01:10 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-space = 
07/24 00:01:16 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-start = ┌──────────────────────────────────────────────────────────┐
07/24 00:01:16 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-title = │              连锁头寸策略监控 [0_om4wiz] - 00:01:16              │
07/24 00:01:16 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-separator = ├──────────────────────────────────────────────────────────┤
07/24 00:01:16 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔄 新轮询周期已启动 = {"pollingCycle":30,"pollingInterval":12000,"monitoringInterval":12}
07/24 00:01:17 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 开始调用PositionAnalyticsService获取分析数据 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2,"cycleId":30}
07/24 00:01:22 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ PositionAnalyticsService数据获取成功 = {"currentPrice":0.000003511233338969899,"positionValue":7.997683629858234,"netPnL":0.004512855514871461,"netPnLPercentage":0.056410693935893264,"activeBin":-568,"positionLowerBin":-693,"positionUpperBin":-556,"holdingDuration":0.09705388888888888}
07/24 00:01:22 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取开始 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:01:22 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:01:22 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 收益提取条件检查 = {"currentPendingYield":"0.006829225656637766","yieldExtractionThreshold":"0.02","canExtract":false,"activeBin":-568,"positionLowerBin":-693,"positionUpperBin":-556}
07/24 00:01:22 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取完成 = {"currentPendingYield":"0.006829225656637766","totalExtractedYield":"0"}
07/24 00:01:22 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:01:22 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🏗️ 头寸重建决策结果 = {"shouldRecreate":false,"reason":"No recreation needed (智能选择: 默认状态)","recreationType":"NONE","confidence":0,"urgency":"low","recommendedAction":"Continue monitoring","hasOutOfRangeDetails":false,"timeRemaining":null}
07/24 00:01:22 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🎯 智能状态选择详情 = {"selectedReason":"No recreation needed (智能选择: 默认状态)","stateType":"NONE","confidence":0}
07/24 00:01:22 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"condition":true}
07/24 00:01:22 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"isThresholdValid":true,"currentSwitchEnabled":false,"lastSwitchUpdateTime":"2025-07-23T15:55:04.891Z"}
07/24 00:01:22 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":30,"cacheAge":1,"reason":"防止重复调用PositionAnalyticsService"}
07/24 00:01:22 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 动态重建开关数据分析 = {"benchmarkYield5Min":null,"threshold":2,"isDataValid":false,"comparisonResult":"数据无效","willUpdateSwitch":false}
07/24 00:01:22 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 进入updateDynamicRecreationSwitch方法 = {"instanceId":"chain_position_1753284004910_om4wiz","benchmarkYield5Min":null,"configThreshold":2,"currentSwitchState":false}
07/24 00:01:22 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ⚠️ 基准收益率数据无效，跳过开关状态更新 = {"benchmarkYield5Min":null,"reason":"null值"}
07/24 00:01:22 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":30,"cacheAge":2,"reason":"防止重复调用PositionAnalyticsService"}
07/24 00:01:22 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🧠 智能止损分析完成 = {"决策行动":"继续持有","置信度":"90.00%","风险评分":"12.00","紧急程度":"低","分析原因":["位置安全: 活跃bin位置91.2%，高于30%安全线"]}
07/24 00:01:22 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📡 智能止损数据已广播到前端 = {"instanceId":"chain_position_1753284004910_om4wiz","action":"HOLD","confidence":90}
07/24 00:01:22 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ 策略继续运行 = {"置信度":"90.00%","风险评分":"12.00","策略状态":"正常运行中"}
07/24 00:01:22 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-end = └──────────────────────────────────────────────────────────┘
07/24 00:01:22 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-space = 
07/24 00:01:28 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-start = ┌──────────────────────────────────────────────────────────┐
07/24 00:01:28 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-title = │              连锁头寸策略监控 [0_om4wiz] - 00:01:28              │
07/24 00:01:28 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-separator = ├──────────────────────────────────────────────────────────┤
07/24 00:01:28 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔄 新轮询周期已启动 = {"pollingCycle":31,"pollingInterval":12000,"monitoringInterval":12}
07/24 00:01:29 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 开始调用PositionAnalyticsService获取分析数据 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2,"cycleId":31}
07/24 00:01:35 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ PositionAnalyticsService数据获取成功 = {"currentPrice":0.000003546345672359598,"positionValue":7.998176896076743,"netPnL":0.005054096289947907,"netPnLPercentage":0.06317620362434884,"activeBin":-567,"positionLowerBin":-693,"positionUpperBin":-556,"holdingDuration":0.10063083333333334}
07/24 00:01:35 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取开始 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:01:35 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:01:35 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 收益提取条件检查 = {"currentPendingYield":"0.0068772002132041436","yieldExtractionThreshold":"0.02","canExtract":false,"activeBin":-567,"positionLowerBin":-693,"positionUpperBin":-556}
07/24 00:01:35 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取完成 = {"currentPendingYield":"0.0068772002132041436","totalExtractedYield":"0"}
07/24 00:01:35 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:01:35 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🏗️ 头寸重建决策结果 = {"shouldRecreate":false,"reason":"No recreation needed (智能选择: 默认状态)","recreationType":"NONE","confidence":0,"urgency":"low","recommendedAction":"Continue monitoring","hasOutOfRangeDetails":false,"timeRemaining":null}
07/24 00:01:35 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🎯 智能状态选择详情 = {"selectedReason":"No recreation needed (智能选择: 默认状态)","stateType":"NONE","confidence":0}
07/24 00:01:35 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"condition":true}
07/24 00:01:35 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"isThresholdValid":true,"currentSwitchEnabled":false,"lastSwitchUpdateTime":"2025-07-23T15:55:04.891Z"}
07/24 00:01:35 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":31,"cacheAge":1,"reason":"防止重复调用PositionAnalyticsService"}
07/24 00:01:35 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 动态重建开关数据分析 = {"benchmarkYield5Min":null,"threshold":2,"isDataValid":false,"comparisonResult":"数据无效","willUpdateSwitch":false}
07/24 00:01:35 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 进入updateDynamicRecreationSwitch方法 = {"instanceId":"chain_position_1753284004910_om4wiz","benchmarkYield5Min":null,"configThreshold":2,"currentSwitchState":false}
07/24 00:01:35 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ⚠️ 基准收益率数据无效，跳过开关状态更新 = {"benchmarkYield5Min":null,"reason":"null值"}
07/24 00:01:35 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":31,"cacheAge":2,"reason":"防止重复调用PositionAnalyticsService"}
07/24 00:01:35 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🧠 智能止损分析完成 = {"决策行动":"继续持有","置信度":"90.00%","风险评分":"12.00","紧急程度":"低","分析原因":["位置安全: 活跃bin位置92.0%，高于30%安全线"]}
07/24 00:01:35 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📡 智能止损数据已广播到前端 = {"instanceId":"chain_position_1753284004910_om4wiz","action":"HOLD","confidence":90}
07/24 00:01:35 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ 策略继续运行 = {"置信度":"90.00%","风险评分":"12.00","策略状态":"正常运行中"}
07/24 00:01:35 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-end = └──────────────────────────────────────────────────────────┘
07/24 00:01:35 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-space = 
07/24 00:01:40 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-start = ┌──────────────────────────────────────────────────────────┐
07/24 00:01:40 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-title = │              连锁头寸策略监控 [0_om4wiz] - 00:01:40              │
07/24 00:01:40 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-separator = ├──────────────────────────────────────────────────────────┤
07/24 00:01:40 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔄 新轮询周期已启动 = {"pollingCycle":32,"pollingInterval":12000,"monitoringInterval":12}
07/24 00:01:41 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 开始调用PositionAnalyticsService获取分析数据 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2,"cycleId":32}
07/24 00:01:44 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ PositionAnalyticsService数据获取成功 = {"currentPrice":0.000003546345672359598,"positionValue":7.998176896076743,"netPnL":0.005054096289947907,"netPnLPercentage":0.06317620362434884,"activeBin":-567,"positionLowerBin":-693,"positionUpperBin":-556,"holdingDuration":0.10324805555555555}
07/24 00:01:44 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取开始 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:01:44 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:01:44 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 收益提取条件检查 = {"currentPendingYield":"0.0068772002132041436","yieldExtractionThreshold":"0.02","canExtract":false,"activeBin":-567,"positionLowerBin":-693,"positionUpperBin":-556}
07/24 00:01:44 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取完成 = {"currentPendingYield":"0.0068772002132041436","totalExtractedYield":"0"}
07/24 00:01:44 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:01:44 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🏗️ 头寸重建决策结果 = {"shouldRecreate":false,"reason":"No recreation needed (智能选择: 默认状态)","recreationType":"NONE","confidence":0,"urgency":"low","recommendedAction":"Continue monitoring","hasOutOfRangeDetails":false,"timeRemaining":null}
07/24 00:01:44 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🎯 智能状态选择详情 = {"selectedReason":"No recreation needed (智能选择: 默认状态)","stateType":"NONE","confidence":0}
07/24 00:01:44 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"condition":true}
07/24 00:01:44 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"isThresholdValid":true,"currentSwitchEnabled":false,"lastSwitchUpdateTime":"2025-07-23T15:55:04.891Z"}
07/24 00:01:44 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":32,"cacheAge":1,"reason":"防止重复调用PositionAnalyticsService"}
07/24 00:01:44 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 动态重建开关数据分析 = {"benchmarkYield5Min":null,"threshold":2,"isDataValid":false,"comparisonResult":"数据无效","willUpdateSwitch":false}
07/24 00:01:44 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 进入updateDynamicRecreationSwitch方法 = {"instanceId":"chain_position_1753284004910_om4wiz","benchmarkYield5Min":null,"configThreshold":2,"currentSwitchState":false}
07/24 00:01:44 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ⚠️ 基准收益率数据无效，跳过开关状态更新 = {"benchmarkYield5Min":null,"reason":"null值"}
07/24 00:01:44 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":32,"cacheAge":2,"reason":"防止重复调用PositionAnalyticsService"}
07/24 00:01:44 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🧠 智能止损分析完成 = {"决策行动":"继续持有","置信度":"90.00%","风险评分":"13.00","紧急程度":"低","分析原因":["位置安全: 活跃bin位置92.0%，高于30%安全线"]}
07/24 00:01:44 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📡 智能止损数据已广播到前端 = {"instanceId":"chain_position_1753284004910_om4wiz","action":"HOLD","confidence":90}
07/24 00:01:44 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ 策略继续运行 = {"置信度":"90.00%","风险评分":"13.00","策略状态":"正常运行中"}
07/24 00:01:44 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-end = └──────────────────────────────────────────────────────────┘
07/24 00:01:44 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-space = 
07/24 00:01:52 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-start = ┌──────────────────────────────────────────────────────────┐
07/24 00:01:52 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-title = │              连锁头寸策略监控 [0_om4wiz] - 00:01:52              │
07/24 00:01:52 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-separator = ├──────────────────────────────────────────────────────────┤
07/24 00:01:52 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔄 新轮询周期已启动 = {"pollingCycle":33,"pollingInterval":12000,"monitoringInterval":12}
07/24 00:01:53 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 开始调用PositionAnalyticsService获取分析数据 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2,"cycleId":33}
07/24 00:01:58 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ PositionAnalyticsService数据获取成功 = {"currentPrice":0.000003546345672359598,"positionValue":7.998176896076743,"netPnL":0.005054096289947907,"netPnLPercentage":0.06317620362434884,"activeBin":-567,"positionLowerBin":-693,"positionUpperBin":-556,"holdingDuration":0.10717527777777777}
07/24 00:01:58 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取开始 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:01:58 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:01:58 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 收益提取条件检查 = {"currentPendingYield":"0.0068772002132041436","yieldExtractionThreshold":"0.02","canExtract":false,"activeBin":-567,"positionLowerBin":-693,"positionUpperBin":-556}
07/24 00:01:58 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取完成 = {"currentPendingYield":"0.0068772002132041436","totalExtractedYield":"0"}
07/24 00:01:58 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:01:58 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🏗️ 头寸重建决策结果 = {"shouldRecreate":false,"reason":"No recreation needed (智能选择: 默认状态)","recreationType":"NONE","confidence":0,"urgency":"low","recommendedAction":"Continue monitoring","hasOutOfRangeDetails":false,"timeRemaining":null}
07/24 00:01:58 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🎯 智能状态选择详情 = {"selectedReason":"No recreation needed (智能选择: 默认状态)","stateType":"NONE","confidence":0}
07/24 00:01:58 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"condition":true}
07/24 00:01:58 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"isThresholdValid":true,"currentSwitchEnabled":false,"lastSwitchUpdateTime":"2025-07-23T15:55:04.891Z"}
07/24 00:01:58 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":33,"cacheAge":1,"reason":"防止重复调用PositionAnalyticsService"}
07/24 00:01:58 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 动态重建开关数据分析 = {"benchmarkYield5Min":null,"threshold":2,"isDataValid":false,"comparisonResult":"数据无效","willUpdateSwitch":false}
07/24 00:01:58 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 进入updateDynamicRecreationSwitch方法 = {"instanceId":"chain_position_1753284004910_om4wiz","benchmarkYield5Min":null,"configThreshold":2,"currentSwitchState":false}
07/24 00:01:58 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ⚠️ 基准收益率数据无效，跳过开关状态更新 = {"benchmarkYield5Min":null,"reason":"null值"}
07/24 00:01:58 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":33,"cacheAge":2,"reason":"防止重复调用PositionAnalyticsService"}
07/24 00:01:58 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🧠 智能止损分析完成 = {"决策行动":"继续持有","置信度":"90.00%","风险评分":"12.00","紧急程度":"低","分析原因":["位置安全: 活跃bin位置92.0%，高于30%安全线"]}
07/24 00:01:58 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📡 智能止损数据已广播到前端 = {"instanceId":"chain_position_1753284004910_om4wiz","action":"HOLD","confidence":90}
07/24 00:01:58 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ 策略继续运行 = {"置信度":"90.00%","风险评分":"12.00","策略状态":"正常运行中"}
07/24 00:01:58 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-end = └──────────────────────────────────────────────────────────┘
07/24 00:01:58 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-space = 
07/24 00:02:04 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-start = ┌──────────────────────────────────────────────────────────┐
07/24 00:02:04 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-title = │              连锁头寸策略监控 [0_om4wiz] - 00:02:04              │
07/24 00:02:04 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-separator = ├──────────────────────────────────────────────────────────┤
07/24 00:02:04 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔄 新轮询周期已启动 = {"pollingCycle":34,"pollingInterval":12000,"monitoringInterval":12}
07/24 00:02:05 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 开始调用PositionAnalyticsService获取分析数据 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2,"cycleId":34}
07/24 00:02:07 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ PositionAnalyticsService数据获取成功 = {"currentPrice":0.000003546345672359598,"positionValue":7.998176896076743,"netPnL":0.005054096289947907,"netPnLPercentage":0.06317620362434884,"activeBin":-567,"positionLowerBin":-693,"positionUpperBin":-556,"holdingDuration":0.109695}
07/24 00:02:07 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取开始 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:02:07 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:02:07 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 收益提取条件检查 = {"currentPendingYield":"0.0068772002132041436","yieldExtractionThreshold":"0.02","canExtract":false,"activeBin":-567,"positionLowerBin":-693,"positionUpperBin":-556}
07/24 00:02:07 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取完成 = {"currentPendingYield":"0.0068772002132041436","totalExtractedYield":"0"}
07/24 00:02:07 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:02:07 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🏗️ 头寸重建决策结果 = {"shouldRecreate":false,"reason":"No recreation needed (智能选择: 默认状态)","recreationType":"NONE","confidence":0,"urgency":"low","recommendedAction":"Continue monitoring","hasOutOfRangeDetails":false,"timeRemaining":null}
07/24 00:02:07 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🎯 智能状态选择详情 = {"selectedReason":"No recreation needed (智能选择: 默认状态)","stateType":"NONE","confidence":0}
07/24 00:02:07 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"condition":true}
07/24 00:02:07 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"isThresholdValid":true,"currentSwitchEnabled":false,"lastSwitchUpdateTime":"2025-07-23T15:55:04.891Z"}
07/24 00:02:07 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":34,"cacheAge":1,"reason":"防止重复调用PositionAnalyticsService"}
07/24 00:02:07 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 动态重建开关数据分析 = {"benchmarkYield5Min":null,"threshold":2,"isDataValid":false,"comparisonResult":"数据无效","willUpdateSwitch":false}
07/24 00:02:07 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 进入updateDynamicRecreationSwitch方法 = {"instanceId":"chain_position_1753284004910_om4wiz","benchmarkYield5Min":null,"configThreshold":2,"currentSwitchState":false}
07/24 00:02:07 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ⚠️ 基准收益率数据无效，跳过开关状态更新 = {"benchmarkYield5Min":null,"reason":"null值"}
07/24 00:02:07 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":34,"cacheAge":2,"reason":"防止重复调用PositionAnalyticsService"}
07/24 00:02:07 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🧠 智能止损分析完成 = {"决策行动":"继续持有","置信度":"90.00%","风险评分":"14.00","紧急程度":"低","分析原因":["位置安全: 活跃bin位置92.0%，高于30%安全线"]}
07/24 00:02:07 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📡 智能止损数据已广播到前端 = {"instanceId":"chain_position_1753284004910_om4wiz","action":"HOLD","confidence":90}
07/24 00:02:07 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ 策略继续运行 = {"置信度":"90.00%","风险评分":"14.00","策略状态":"正常运行中"}
07/24 00:02:07 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-end = └──────────────────────────────────────────────────────────┘
07/24 00:02:07 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-space = 
07/24 00:02:16 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-start = ┌──────────────────────────────────────────────────────────┐
07/24 00:02:16 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-title = │              连锁头寸策略监控 [0_om4wiz] - 00:02:16              │
07/24 00:02:16 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-separator = ├──────────────────────────────────────────────────────────┤
07/24 00:02:16 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔄 新轮询周期已启动 = {"pollingCycle":35,"pollingInterval":12000,"monitoringInterval":12}
07/24 00:02:17 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 开始调用PositionAnalyticsService获取分析数据 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2,"cycleId":35}
07/24 00:02:21 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ PositionAnalyticsService数据获取成功 = {"currentPrice":0.000002906387985686907,"positionValue":7.966727935576008,"netPnL":-0.01206749306959054,"netPnLPercentage":-0.15084366336988175,"activeBin":-587,"positionLowerBin":-693,"positionUpperBin":-556,"holdingDuration":0.11341083333333334}
07/24 00:02:21 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取开始 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:02:21 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:02:21 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 收益提取条件检查 = {"currentPendingYield":"0.021204571354401305","yieldExtractionThreshold":"0.02","canExtract":true,"activeBin":-587,"positionLowerBin":-693,"positionUpperBin":-556}
07/24 00:02:28 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-start = ┌──────────────────────────────────────────────────────────┐
07/24 00:02:28 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-title = │              连锁头寸策略监控 [0_om4wiz] - 00:02:28              │
07/24 00:02:28 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-separator = ├──────────────────────────────────────────────────────────┤
07/24 00:02:28 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔄 新轮询周期已启动 = {"pollingCycle":36,"pollingInterval":12000,"monitoringInterval":12}
07/24 00:02:29 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 开始调用PositionAnalyticsService获取分析数据 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2,"cycleId":36}
07/24 00:02:33 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ PositionAnalyticsService数据获取成功 = {"currentPrice":0.000002792981727686122,"positionValue":7.953179845982624,"netPnL":-0.022023139842999306,"netPnLPercentage":-0.27528924803749133,"activeBin":-591,"positionLowerBin":-693,"positionUpperBin":-556,"holdingDuration":0.11679027777777778}
07/24 00:02:33 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取开始 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:02:33 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:02:33 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 收益提取条件检查 = {"currentPendingYield":"0.024797014174376627","yieldExtractionThreshold":"0.02","canExtract":true,"activeBin":-591,"positionLowerBin":-693,"positionUpperBin":-556}
07/24 00:02:40 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-start = ┌──────────────────────────────────────────────────────────┐
07/24 00:02:40 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-title = │              连锁头寸策略监控 [0_om4wiz] - 00:02:40              │
07/24 00:02:40 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-separator = ├──────────────────────────────────────────────────────────┤
07/24 00:02:40 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔄 新轮询周期已启动 = {"pollingCycle":37,"pollingInterval":12000,"monitoringInterval":12}
07/24 00:02:41 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 开始调用PositionAnalyticsService获取分析数据 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2,"cycleId":37}
07/24 00:02:46 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ PositionAnalyticsService数据获取成功 = {"currentPrice":0.0000028209115449629836,"positionValue":7.9569171933736715,"netPnL":-0.016834562451951918,"netPnLPercentage":-0.21043203064939897,"activeBin":-590,"positionLowerBin":-693,"positionUpperBin":-556,"holdingDuration":0.12040944444444444}
07/24 00:02:46 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取开始 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:02:46 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:02:46 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 收益提取条件检查 = {"currentPendingYield":"0.00145123","yieldExtractionThreshold":"0.02","canExtract":false,"activeBin":-590,"positionLowerBin":-693,"positionUpperBin":-556}
07/24 00:02:46 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取完成 = {"currentPendingYield":"0.00145123","totalExtractedYield":"0.024797014174376627"}
07/24 00:02:46 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:02:46 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🏗️ 头寸重建决策结果 = {"shouldRecreate":false,"reason":"No recreation needed (智能选择: 默认状态)","recreationType":"NONE","confidence":0,"urgency":"low","recommendedAction":"Continue monitoring","hasOutOfRangeDetails":false,"timeRemaining":null}
07/24 00:02:46 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🎯 智能状态选择详情 = {"selectedReason":"No recreation needed (智能选择: 默认状态)","stateType":"NONE","confidence":0}
07/24 00:02:46 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"condition":true}
07/24 00:02:46 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"isThresholdValid":true,"currentSwitchEnabled":false,"lastSwitchUpdateTime":"2025-07-23T15:55:04.891Z"}
07/24 00:02:46 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":37,"cacheAge":0,"reason":"防止重复调用PositionAnalyticsService"}
07/24 00:02:46 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 动态重建开关数据分析 = {"benchmarkYield5Min":null,"threshold":2,"isDataValid":false,"comparisonResult":"数据无效","willUpdateSwitch":false}
07/24 00:02:46 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 进入updateDynamicRecreationSwitch方法 = {"instanceId":"chain_position_1753284004910_om4wiz","benchmarkYield5Min":null,"configThreshold":2,"currentSwitchState":false}
07/24 00:02:46 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ⚠️ 基准收益率数据无效，跳过开关状态更新 = {"benchmarkYield5Min":null,"reason":"null值"}
07/24 00:02:46 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":37,"cacheAge":1,"reason":"防止重复调用PositionAnalyticsService"}
07/24 00:02:46 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🧠 智能止损分析完成 = {"决策行动":"继续持有","置信度":"90.00%","风险评分":"31.90","紧急程度":"低","分析原因":["位置安全: 活跃bin位置75.2%，高于30%安全线"]}
07/24 00:02:46 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📡 智能止损数据已广播到前端 = {"instanceId":"chain_position_1753284004910_om4wiz","action":"HOLD","confidence":90}
07/24 00:02:46 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ 策略继续运行 = {"置信度":"90.00%","风险评分":"31.90","策略状态":"正常运行中"}
07/24 00:02:46 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-end = └──────────────────────────────────────────────────────────┘
07/24 00:02:46 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-space = 
07/24 00:02:48 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取完成 = {"currentPendingYield":"0.024797014174376627","totalExtractedYield":"0"}
07/24 00:02:48 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:02:48 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🏗️ 头寸重建决策结果 = {"shouldRecreate":false,"reason":"No recreation needed (智能选择: 默认状态)","recreationType":"NONE","confidence":0,"urgency":"low","recommendedAction":"Continue monitoring","hasOutOfRangeDetails":false,"timeRemaining":null}
07/24 00:02:48 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🎯 智能状态选择详情 = {"selectedReason":"No recreation needed (智能选择: 默认状态)","stateType":"NONE","confidence":0}
07/24 00:02:48 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"condition":true}
07/24 00:02:48 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"isThresholdValid":true,"currentSwitchEnabled":false,"lastSwitchUpdateTime":"2025-07-23T15:55:04.891Z"}
07/24 00:02:48 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 开始调用PositionAnalyticsService获取分析数据 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2,"cycleId":37}
07/24 00:02:48 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ PositionAnalyticsService数据获取成功 = {"currentPrice":0.0000028209115449629836,"positionValue":7.9569171933736715,"netPnL":-0.016834562451951918,"netPnLPercentage":-0.21043203064939897,"activeBin":-590,"positionLowerBin":-693,"positionUpperBin":-556,"holdingDuration":0.12096972222222223}
07/24 00:02:48 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取开始 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:02:48 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:02:48 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 收益提取条件检查 = {"currentPendingYield":"0.00145123","yieldExtractionThreshold":"0.02","canExtract":false,"activeBin":-590,"positionLowerBin":-693,"positionUpperBin":-556}
07/24 00:02:48 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取完成 = {"currentPendingYield":"0.00145123","totalExtractedYield":"0.024797014174376627"}
07/24 00:02:48 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:02:48 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 动态重建开关数据分析 = {"benchmarkYield5Min":null,"threshold":2,"isDataValid":false,"comparisonResult":"数据无效","willUpdateSwitch":false}
07/24 00:02:48 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 进入updateDynamicRecreationSwitch方法 = {"instanceId":"chain_position_1753284004910_om4wiz","benchmarkYield5Min":null,"configThreshold":2,"currentSwitchState":false}
07/24 00:02:48 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ⚠️ 基准收益率数据无效，跳过开关状态更新 = {"benchmarkYield5Min":null,"reason":"null值"}
07/24 00:02:48 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":37,"cacheAge":1,"reason":"防止重复调用PositionAnalyticsService"}
07/24 00:02:48 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🧠 智能止损分析完成 = {"决策行动":"继续持有","置信度":"90.00%","风险评分":"31.90","紧急程度":"低","分析原因":["位置安全: 活跃bin位置75.2%，高于30%安全线"]}
07/24 00:02:48 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📡 智能止损数据已广播到前端 = {"instanceId":"chain_position_1753284004910_om4wiz","action":"HOLD","confidence":90}
07/24 00:02:48 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ 策略继续运行 = {"置信度":"90.00%","风险评分":"31.90","策略状态":"正常运行中"}
07/24 00:02:48 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-end = └──────────────────────────────────────────────────────────┘
07/24 00:02:48 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-space = 
07/24 00:02:52 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-start = ┌──────────────────────────────────────────────────────────┐
07/24 00:02:52 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-title = │              连锁头寸策略监控 [0_om4wiz] - 00:02:52              │
07/24 00:02:52 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-separator = ├──────────────────────────────────────────────────────────┤
07/24 00:02:52 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔄 新轮询周期已启动 = {"pollingCycle":38,"pollingInterval":12000,"monitoringInterval":12}
07/24 00:02:53 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 开始调用PositionAnalyticsService获取分析数据 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2,"cycleId":38}
07/24 00:02:55 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ PositionAnalyticsService数据获取成功 = {"currentPrice":0.0000028776118670167396,"positionValue":7.964056067420805,"netPnL":-0.006738738404818534,"netPnLPercentage":-0.08423423006023167,"activeBin":-588,"positionLowerBin":-693,"positionUpperBin":-556,"holdingDuration":0.12306333333333333}
07/24 00:02:55 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取开始 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:02:55 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:02:55 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 收益提取条件检查 = {"currentPendingYield":"0.00440818","yieldExtractionThreshold":"0.02","canExtract":false,"activeBin":-588,"positionLowerBin":-693,"positionUpperBin":-556}
07/24 00:02:55 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取完成 = {"currentPendingYield":"0.00440818","totalExtractedYield":"0.024797014174376627"}
07/24 00:02:55 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:02:55 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🏗️ 头寸重建决策结果 = {"shouldRecreate":false,"reason":"No recreation needed (智能选择: 默认状态)","recreationType":"NONE","confidence":0,"urgency":"low","recommendedAction":"Continue monitoring","hasOutOfRangeDetails":false,"timeRemaining":null}
07/24 00:02:55 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🎯 智能状态选择详情 = {"selectedReason":"No recreation needed (智能选择: 默认状态)","stateType":"NONE","confidence":0}
07/24 00:02:55 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"condition":true}
07/24 00:02:55 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"isThresholdValid":true,"currentSwitchEnabled":false,"lastSwitchUpdateTime":"2025-07-23T15:55:04.891Z"}
07/24 00:02:55 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":38,"cacheAge":1,"reason":"防止重复调用PositionAnalyticsService"}
07/24 00:02:55 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 动态重建开关数据分析 = {"benchmarkYield5Min":null,"threshold":2,"isDataValid":false,"comparisonResult":"数据无效","willUpdateSwitch":false}
07/24 00:02:55 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 进入updateDynamicRecreationSwitch方法 = {"instanceId":"chain_position_1753284004910_om4wiz","benchmarkYield5Min":null,"configThreshold":2,"currentSwitchState":false}
07/24 00:02:55 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ⚠️ 基准收益率数据无效，跳过开关状态更新 = {"benchmarkYield5Min":null,"reason":"null值"}
07/24 00:02:55 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":38,"cacheAge":2,"reason":"防止重复调用PositionAnalyticsService"}
07/24 00:02:55 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🧠 智能止损分析完成 = {"决策行动":"继续持有","置信度":"90.00%","风险评分":"29.00","紧急程度":"低","分析原因":["位置安全: 活跃bin位置76.6%，高于30%安全线"]}
07/24 00:02:55 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📡 智能止损数据已广播到前端 = {"instanceId":"chain_position_1753284004910_om4wiz","action":"HOLD","confidence":90}
07/24 00:02:55 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ 策略继续运行 = {"置信度":"90.00%","风险评分":"29.00","策略状态":"正常运行中"}
07/24 00:02:55 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-end = └──────────────────────────────────────────────────────────┘
07/24 00:02:55 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-space = 
07/24 00:03:04 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取完成 = {"currentPendingYield":"0.021204571354401305","totalExtractedYield":"0"}
07/24 00:03:04 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:03:04 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🏗️ 头寸重建决策结果 = {"shouldRecreate":false,"reason":"No recreation needed (智能选择: 默认状态)","recreationType":"NONE","confidence":0,"urgency":"low","recommendedAction":"Continue monitoring","hasOutOfRangeDetails":false,"timeRemaining":null}
07/24 00:03:04 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🎯 智能状态选择详情 = {"selectedReason":"No recreation needed (智能选择: 默认状态)","stateType":"NONE","confidence":0}
07/24 00:03:04 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"condition":true}
07/24 00:03:04 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"isThresholdValid":true,"currentSwitchEnabled":false,"lastSwitchUpdateTime":"2025-07-23T15:55:04.891Z"}
07/24 00:03:04 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 开始调用PositionAnalyticsService获取分析数据 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2,"cycleId":38}
07/24 00:03:04 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-start = ┌──────────────────────────────────────────────────────────┐
07/24 00:03:04 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-title = │              连锁头寸策略监控 [0_om4wiz] - 00:03:04              │
07/24 00:03:04 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-separator = ├──────────────────────────────────────────────────────────┤
07/24 00:03:04 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔄 新轮询周期已启动 = {"pollingCycle":39,"pollingInterval":12000,"monitoringInterval":12}
07/24 00:03:05 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 开始调用PositionAnalyticsService获取分析数据 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2,"cycleId":39}
07/24 00:03:05 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ PositionAnalyticsService数据获取成功 = {"currentPrice":0.0000028776118670167396,"positionValue":7.964056067420805,"netPnL":-0.006738738404818534,"netPnLPercentage":-0.08423423006023167,"activeBin":-588,"positionLowerBin":-693,"positionUpperBin":-556,"holdingDuration":0.1257838888888889}
07/24 00:03:05 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取开始 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:03:05 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:03:07 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 收益提取条件检查 = {"currentPendingYield":"0.00440818","yieldExtractionThreshold":"0.02","canExtract":false,"activeBin":-588,"positionLowerBin":-693,"positionUpperBin":-556}
07/24 00:03:07 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取完成 = {"currentPendingYield":"0.00440818","totalExtractedYield":"0.024797014174376627"}
07/24 00:03:07 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:03:07 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 动态重建开关数据分析 = {"benchmarkYield5Min":null,"threshold":2,"isDataValid":false,"comparisonResult":"数据无效","willUpdateSwitch":false}
07/24 00:03:07 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 进入updateDynamicRecreationSwitch方法 = {"instanceId":"chain_position_1753284004910_om4wiz","benchmarkYield5Min":null,"configThreshold":2,"currentSwitchState":false}
07/24 00:03:07 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ⚠️ 基准收益率数据无效，跳过开关状态更新 = {"benchmarkYield5Min":null,"reason":"null值"}
07/24 00:03:07 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 开始调用PositionAnalyticsService获取分析数据 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2,"cycleId":39}
07/24 00:03:07 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ PositionAnalyticsService数据获取成功 = {"currentPrice":0.0000028776118670167396,"positionValue":7.963597187673347,"netPnL":-0.007197618152276597,"netPnLPercentage":-0.08997022690345746,"activeBin":-588,"positionLowerBin":-693,"positionUpperBin":-556,"holdingDuration":0.12632555555555555}
07/24 00:03:07 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取开始 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:03:07 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:03:07 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 收益提取条件检查 = {"currentPendingYield":"0.00440818","yieldExtractionThreshold":"0.02","canExtract":false,"activeBin":-588,"positionLowerBin":-693,"positionUpperBin":-556}
07/24 00:03:07 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取完成 = {"currentPendingYield":"0.00440818","totalExtractedYield":"0.024797014174376627"}
07/24 00:03:07 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:03:07 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🧠 智能止损分析完成 = {"决策行动":"继续持有","置信度":"90.00%","风险评分":"28.05","紧急程度":"低","分析原因":["位置安全: 活跃bin位置76.6%，高于30%安全线"]}
07/24 00:03:07 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📡 智能止损数据已广播到前端 = {"instanceId":"chain_position_1753284004910_om4wiz","action":"HOLD","confidence":90}
07/24 00:03:07 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ 策略继续运行 = {"置信度":"90.00%","风险评分":"28.05","策略状态":"正常运行中"}
07/24 00:03:07 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-end = └──────────────────────────────────────────────────────────┘
07/24 00:03:07 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-space = 
07/24 00:03:08 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ PositionAnalyticsService数据获取成功 = {"currentPrice":0.0000028776118670167396,"positionValue":7.963597187673347,"netPnL":-0.007197618152276597,"netPnLPercentage":-0.08997022690345746,"activeBin":-588,"positionLowerBin":-693,"positionUpperBin":-556,"holdingDuration":0.12659222222222222}
07/24 00:03:08 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取开始 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:03:08 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:03:08 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 收益提取条件检查 = {"currentPendingYield":"0.00440818","yieldExtractionThreshold":"0.02","canExtract":false,"activeBin":-588,"positionLowerBin":-693,"positionUpperBin":-556}
07/24 00:03:08 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取完成 = {"currentPendingYield":"0.00440818","totalExtractedYield":"0.024797014174376627"}
07/24 00:03:08 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:03:08 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🏗️ 头寸重建决策结果 = {"shouldRecreate":false,"reason":"No recreation needed (智能选择: 默认状态)","recreationType":"NONE","confidence":0,"urgency":"low","recommendedAction":"Continue monitoring","hasOutOfRangeDetails":false,"timeRemaining":null}
07/24 00:03:08 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🎯 智能状态选择详情 = {"selectedReason":"No recreation needed (智能选择: 默认状态)","stateType":"NONE","confidence":0}
07/24 00:03:08 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"condition":true}
07/24 00:03:08 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"isThresholdValid":true,"currentSwitchEnabled":false,"lastSwitchUpdateTime":"2025-07-23T15:55:04.891Z"}
07/24 00:03:08 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":39,"cacheAge":6,"reason":"防止重复调用PositionAnalyticsService"}
07/24 00:03:08 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 动态重建开关数据分析 = {"benchmarkYield5Min":null,"threshold":2,"isDataValid":false,"comparisonResult":"数据无效","willUpdateSwitch":false}
07/24 00:03:08 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 进入updateDynamicRecreationSwitch方法 = {"instanceId":"chain_position_1753284004910_om4wiz","benchmarkYield5Min":null,"configThreshold":2,"currentSwitchState":false}
07/24 00:03:08 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ⚠️ 基准收益率数据无效，跳过开关状态更新 = {"benchmarkYield5Min":null,"reason":"null值"}
07/24 00:03:08 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":39,"cacheAge":8,"reason":"防止重复调用PositionAnalyticsService"}
07/24 00:03:08 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🧠 智能止损分析完成 = {"决策行动":"继续持有","置信度":"90.00%","风险评分":"30.01","紧急程度":"低","分析原因":["位置安全: 活跃bin位置76.6%，高于30%安全线"]}
07/24 00:03:08 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📡 智能止损数据已广播到前端 = {"instanceId":"chain_position_1753284004910_om4wiz","action":"HOLD","confidence":90}
07/24 00:03:08 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ 策略继续运行 = {"置信度":"90.00%","风险评分":"30.01","策略状态":"正常运行中"}
07/24 00:03:08 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-end = └──────────────────────────────────────────────────────────┘
07/24 00:03:08 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-space = 
07/24 00:03:16 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-start = ┌──────────────────────────────────────────────────────────┐
07/24 00:03:16 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-title = │              连锁头寸策略监控 [0_om4wiz] - 00:03:16              │
07/24 00:03:16 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-separator = ├──────────────────────────────────────────────────────────┤
07/24 00:03:16 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔄 新轮询周期已启动 = {"pollingCycle":40,"pollingInterval":12000,"monitoringInterval":12}
07/24 00:03:17 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 开始调用PositionAnalyticsService获取分析数据 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2,"cycleId":40}
07/24 00:03:22 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ PositionAnalyticsService数据获取成功 = {"currentPrice":0.0000028776118670167396,"positionValue":7.963597074117028,"netPnL":-0.007197731708595612,"netPnLPercentage":-0.08997164635744515,"activeBin":-588,"positionLowerBin":-693,"positionUpperBin":-556,"holdingDuration":0.1303425}
07/24 00:03:22 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取开始 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:03:22 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:03:22 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 收益提取条件检查 = {"currentPendingYield":"0.00440818","yieldExtractionThreshold":"0.02","canExtract":false,"activeBin":-588,"positionLowerBin":-693,"positionUpperBin":-556}
07/24 00:03:22 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取完成 = {"currentPendingYield":"0.00440818","totalExtractedYield":"0.024797014174376627"}
07/24 00:03:22 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:03:22 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🏗️ 头寸重建决策结果 = {"shouldRecreate":false,"reason":"No recreation needed (智能选择: 默认状态)","recreationType":"NONE","confidence":0,"urgency":"low","recommendedAction":"Continue monitoring","hasOutOfRangeDetails":false,"timeRemaining":null}
07/24 00:03:22 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🎯 智能状态选择详情 = {"selectedReason":"No recreation needed (智能选择: 默认状态)","stateType":"NONE","confidence":0}
07/24 00:03:22 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"condition":true}
07/24 00:03:22 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"isThresholdValid":true,"currentSwitchEnabled":false,"lastSwitchUpdateTime":"2025-07-23T15:55:04.891Z"}
07/24 00:03:22 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":40,"cacheAge":1,"reason":"防止重复调用PositionAnalyticsService"}
07/24 00:03:22 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 动态重建开关数据分析 = {"benchmarkYield5Min":null,"threshold":2,"isDataValid":false,"comparisonResult":"数据无效","willUpdateSwitch":false}
07/24 00:03:22 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 进入updateDynamicRecreationSwitch方法 = {"instanceId":"chain_position_1753284004910_om4wiz","benchmarkYield5Min":null,"configThreshold":2,"currentSwitchState":false}
07/24 00:03:22 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ⚠️ 基准收益率数据无效，跳过开关状态更新 = {"benchmarkYield5Min":null,"reason":"null值"}
07/24 00:03:22 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":40,"cacheAge":2,"reason":"防止重复调用PositionAnalyticsService"}
07/24 00:03:22 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🧠 智能止损分析完成 = {"决策行动":"继续持有","置信度":"90.00%","风险评分":"12.05","紧急程度":"低","分析原因":["位置安全: 活跃bin位置76.6%，高于30%安全线"]}
07/24 00:03:22 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📡 智能止损数据已广播到前端 = {"instanceId":"chain_position_1753284004910_om4wiz","action":"HOLD","confidence":90}
07/24 00:03:22 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ 策略继续运行 = {"置信度":"90.00%","风险评分":"12.05","策略状态":"正常运行中"}
07/24 00:03:22 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-end = └──────────────────────────────────────────────────────────┘
07/24 00:03:22 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-space = 
07/24 00:03:28 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-start = ┌──────────────────────────────────────────────────────────┐
07/24 00:03:28 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-title = │              连锁头寸策略监控 [0_om4wiz] - 00:03:28              │
07/24 00:03:28 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-separator = ├──────────────────────────────────────────────────────────┤
07/24 00:03:28 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔄 新轮询周期已启动 = {"pollingCycle":41,"pollingInterval":12000,"monitoringInterval":12}
07/24 00:03:29 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 开始调用PositionAnalyticsService获取分析数据 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2,"cycleId":41}
07/24 00:03:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ PositionAnalyticsService数据获取成功 = {"currentPrice":0.000002935451865543776,"positionValue":7.96989697840678,"netPnL":-0.0008978274188438107,"netPnLPercentage":-0.011222842735547633,"activeBin":-586,"positionLowerBin":-693,"positionUpperBin":-556,"holdingDuration":0.13319166666666668}
07/24 00:03:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取开始 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:03:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:03:33 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 收益提取条件检查 = {"currentPendingYield":"0.00440818","yieldExtractionThreshold":"0.02","canExtract":false,"activeBin":-586,"positionLowerBin":-693,"positionUpperBin":-556}
07/24 00:03:33 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取完成 = {"currentPendingYield":"0.00440818","totalExtractedYield":"0.024797014174376627"}
07/24 00:03:33 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:03:33 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🏗️ 头寸重建决策结果 = {"shouldRecreate":false,"reason":"No recreation needed (智能选择: 默认状态)","recreationType":"NONE","confidence":0,"urgency":"low","recommendedAction":"Continue monitoring","hasOutOfRangeDetails":false,"timeRemaining":null}
07/24 00:03:33 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🎯 智能状态选择详情 = {"selectedReason":"No recreation needed (智能选择: 默认状态)","stateType":"NONE","confidence":0}
07/24 00:03:33 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"condition":true}
07/24 00:03:33 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"isThresholdValid":true,"currentSwitchEnabled":false,"lastSwitchUpdateTime":"2025-07-23T15:55:04.891Z"}
07/24 00:03:33 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":41,"cacheAge":1,"reason":"防止重复调用PositionAnalyticsService"}
07/24 00:03:33 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 动态重建开关数据分析 = {"benchmarkYield5Min":null,"threshold":2,"isDataValid":false,"comparisonResult":"数据无效","willUpdateSwitch":false}
07/24 00:03:33 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 进入updateDynamicRecreationSwitch方法 = {"instanceId":"chain_position_1753284004910_om4wiz","benchmarkYield5Min":null,"configThreshold":2,"currentSwitchState":false}
07/24 00:03:33 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ⚠️ 基准收益率数据无效，跳过开关状态更新 = {"benchmarkYield5Min":null,"reason":"null值"}
07/24 00:03:33 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":41,"cacheAge":2,"reason":"防止重复调用PositionAnalyticsService"}
07/24 00:03:33 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🧠 智能止损分析完成 = {"决策行动":"继续持有","置信度":"90.00%","风险评分":"14.01","紧急程度":"低","分析原因":["位置安全: 活跃bin位置78.1%，高于30%安全线"]}
07/24 00:03:33 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📡 智能止损数据已广播到前端 = {"instanceId":"chain_position_1753284004910_om4wiz","action":"HOLD","confidence":90}
07/24 00:03:33 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ 策略继续运行 = {"置信度":"90.00%","风险评分":"14.01","策略状态":"正常运行中"}
07/24 00:03:33 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-end = └──────────────────────────────────────────────────────────┘
07/24 00:03:33 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-space = 
07/24 00:03:40 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-start = ┌──────────────────────────────────────────────────────────┐
07/24 00:03:40 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-title = │              连锁头寸策略监控 [0_om4wiz] - 00:03:40              │
07/24 00:03:40 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-separator = ├──────────────────────────────────────────────────────────┤
07/24 00:03:40 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔄 新轮询周期已启动 = {"pollingCycle":42,"pollingInterval":12000,"monitoringInterval":12}
07/24 00:03:41 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 开始调用PositionAnalyticsService获取分析数据 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2,"cycleId":42}
07/24 00:03:44 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ PositionAnalyticsService数据获取成功 = {"currentPrice":0.000002935451865543776,"positionValue":7.969684216439231,"netPnL":0.0010188306136083014,"netPnLPercentage":0.012735382670103768,"activeBin":-586,"positionLowerBin":-693,"positionUpperBin":-556,"holdingDuration":0.1364886111111111}
07/24 00:03:44 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取开始 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:03:44 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:03:46 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 收益提取条件检查 = {"currentPendingYield":"0.0065376","yieldExtractionThreshold":"0.02","canExtract":false,"activeBin":-586,"positionLowerBin":-693,"positionUpperBin":-556}
07/24 00:03:46 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取完成 = {"currentPendingYield":"0.0065376","totalExtractedYield":"0.024797014174376627"}
07/24 00:03:46 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:03:46 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🏗️ 头寸重建决策结果 = {"shouldRecreate":false,"reason":"No recreation needed (智能选择: 默认状态)","recreationType":"NONE","confidence":0,"urgency":"low","recommendedAction":"Continue monitoring","hasOutOfRangeDetails":false,"timeRemaining":null}
07/24 00:03:46 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🎯 智能状态选择详情 = {"selectedReason":"No recreation needed (智能选择: 默认状态)","stateType":"NONE","confidence":0}
07/24 00:03:46 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"condition":true}
07/24 00:03:46 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"isThresholdValid":true,"currentSwitchEnabled":false,"lastSwitchUpdateTime":"2025-07-23T15:55:04.891Z"}
07/24 00:03:46 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":42,"cacheAge":1,"reason":"防止重复调用PositionAnalyticsService"}
07/24 00:03:46 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 动态重建开关数据分析 = {"benchmarkYield5Min":null,"threshold":2,"isDataValid":false,"comparisonResult":"数据无效","willUpdateSwitch":false}
07/24 00:03:46 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 进入updateDynamicRecreationSwitch方法 = {"instanceId":"chain_position_1753284004910_om4wiz","benchmarkYield5Min":null,"configThreshold":2,"currentSwitchState":false}
07/24 00:03:46 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ⚠️ 基准收益率数据无效，跳过开关状态更新 = {"benchmarkYield5Min":null,"reason":"null值"}
07/24 00:03:46 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":42,"cacheAge":2,"reason":"防止重复调用PositionAnalyticsService"}
07/24 00:03:46 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🧠 智能止损分析完成 = {"决策行动":"继续持有","置信度":"90.00%","风险评分":"14.01","紧急程度":"低","分析原因":["位置安全: 活跃bin位置78.1%，高于30%安全线"]}
07/24 00:03:46 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📡 智能止损数据已广播到前端 = {"instanceId":"chain_position_1753284004910_om4wiz","action":"HOLD","confidence":90}
07/24 00:03:46 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ 策略继续运行 = {"置信度":"90.00%","风险评分":"14.01","策略状态":"正常运行中"}
07/24 00:03:46 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-end = └──────────────────────────────────────────────────────────┘
07/24 00:03:46 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-space = 
07/24 00:03:52 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-start = ┌──────────────────────────────────────────────────────────┐
07/24 00:03:52 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-title = │              连锁头寸策略监控 [0_om4wiz] - 00:03:52              │
07/24 00:03:52 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-separator = ├──────────────────────────────────────────────────────────┤
07/24 00:03:52 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔄 新轮询周期已启动 = {"pollingCycle":43,"pollingInterval":12000,"monitoringInterval":12}
07/24 00:03:53 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 开始调用PositionAnalyticsService获取分析数据 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2,"cycleId":43}
07/24 00:03:56 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ PositionAnalyticsService数据获取成功 = {"currentPrice":0.0000029648063841992135,"positionValue":7.972461778603623,"netPnL":0.005014602777999144,"netPnLPercentage":0.0626825347249893,"activeBin":-585,"positionLowerBin":-693,"positionUpperBin":-556,"holdingDuration":0.13996638888888888}
07/24 00:03:56 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取开始 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:03:56 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:03:58 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 收益提取条件检查 = {"currentPendingYield":"0.00775581","yieldExtractionThreshold":"0.02","canExtract":false,"activeBin":-585,"positionLowerBin":-693,"positionUpperBin":-556}
07/24 00:03:58 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取完成 = {"currentPendingYield":"0.00775581","totalExtractedYield":"0.024797014174376627"}
07/24 00:03:58 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:03:58 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🏗️ 头寸重建决策结果 = {"shouldRecreate":false,"reason":"No recreation needed (智能选择: 默认状态)","recreationType":"NONE","confidence":0,"urgency":"low","recommendedAction":"Continue monitoring","hasOutOfRangeDetails":false,"timeRemaining":null}
07/24 00:03:58 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🎯 智能状态选择详情 = {"selectedReason":"No recreation needed (智能选择: 默认状态)","stateType":"NONE","confidence":0}
07/24 00:03:58 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"condition":true}
07/24 00:03:58 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"isThresholdValid":true,"currentSwitchEnabled":false,"lastSwitchUpdateTime":"2025-07-23T15:55:04.891Z"}
07/24 00:03:58 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":43,"cacheAge":1,"reason":"防止重复调用PositionAnalyticsService"}
07/24 00:03:58 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 动态重建开关数据分析 = {"benchmarkYield5Min":null,"threshold":2,"isDataValid":false,"comparisonResult":"数据无效","willUpdateSwitch":false}
07/24 00:03:58 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 进入updateDynamicRecreationSwitch方法 = {"instanceId":"chain_position_1753284004910_om4wiz","benchmarkYield5Min":null,"configThreshold":2,"currentSwitchState":false}
07/24 00:03:58 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ⚠️ 基准收益率数据无效，跳过开关状态更新 = {"benchmarkYield5Min":null,"reason":"null值"}
07/24 00:03:58 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":43,"cacheAge":2,"reason":"防止重复调用PositionAnalyticsService"}
07/24 00:03:58 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🧠 智能止损分析完成 = {"决策行动":"继续持有","置信度":"90.00%","风险评分":"12.00","紧急程度":"低","分析原因":["位置安全: 活跃bin位置78.8%，高于30%安全线"]}
07/24 00:03:58 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📡 智能止损数据已广播到前端 = {"instanceId":"chain_position_1753284004910_om4wiz","action":"HOLD","confidence":90}
07/24 00:03:58 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ 策略继续运行 = {"置信度":"90.00%","风险评分":"12.00","策略状态":"正常运行中"}
07/24 00:03:58 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-end = └──────────────────────────────────────────────────────────┘
07/24 00:03:58 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-space = 
07/24 00:04:04 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-start = ┌──────────────────────────────────────────────────────────┐
07/24 00:04:04 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-title = │              连锁头寸策略监控 [0_om4wiz] - 00:04:04              │
07/24 00:04:04 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-separator = ├──────────────────────────────────────────────────────────┤
07/24 00:04:04 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔄 新轮询周期已启动 = {"pollingCycle":44,"pollingInterval":12000,"monitoringInterval":12}
07/24 00:04:05 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 开始调用PositionAnalyticsService获取分析数据 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2,"cycleId":44}
07/24 00:04:08 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ PositionAnalyticsService数据获取成功 = {"currentPrice":0.000002994454448041206,"positionValue":7.9750686544515625,"netPnL":0.007621478625939204,"netPnLPercentage":0.09526848282424005,"activeBin":-584,"positionLowerBin":-693,"positionUpperBin":-556,"holdingDuration":0.1431963888888889}
07/24 00:04:08 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取开始 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:04:08 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:04:10 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 收益提取条件检查 = {"currentPendingYield":"0.00775581","yieldExtractionThreshold":"0.02","canExtract":false,"activeBin":-584,"positionLowerBin":-693,"positionUpperBin":-556}
07/24 00:04:10 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取完成 = {"currentPendingYield":"0.00775581","totalExtractedYield":"0.024797014174376627"}
07/24 00:04:10 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:04:10 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🏗️ 头寸重建决策结果 = {"shouldRecreate":false,"reason":"No recreation needed (智能选择: 默认状态)","recreationType":"NONE","confidence":0,"urgency":"low","recommendedAction":"Continue monitoring","hasOutOfRangeDetails":false,"timeRemaining":null}
07/24 00:04:10 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🎯 智能状态选择详情 = {"selectedReason":"No recreation needed (智能选择: 默认状态)","stateType":"NONE","confidence":0}
07/24 00:04:10 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"condition":true}
07/24 00:04:10 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"isThresholdValid":true,"currentSwitchEnabled":false,"lastSwitchUpdateTime":"2025-07-23T15:55:04.891Z"}
07/24 00:04:10 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":44,"cacheAge":1,"reason":"防止重复调用PositionAnalyticsService"}
07/24 00:04:10 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 动态重建开关数据分析 = {"benchmarkYield5Min":null,"threshold":2,"isDataValid":false,"comparisonResult":"数据无效","willUpdateSwitch":false}
07/24 00:04:10 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 进入updateDynamicRecreationSwitch方法 = {"instanceId":"chain_position_1753284004910_om4wiz","benchmarkYield5Min":null,"configThreshold":2,"currentSwitchState":false}
07/24 00:04:10 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ⚠️ 基准收益率数据无效，跳过开关状态更新 = {"benchmarkYield5Min":null,"reason":"null值"}
07/24 00:04:10 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":44,"cacheAge":2,"reason":"防止重复调用PositionAnalyticsService"}
07/24 00:04:10 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🧠 智能止损分析完成 = {"决策行动":"继续持有","置信度":"90.00%","风险评分":"13.00","紧急程度":"低","分析原因":["位置安全: 活跃bin位置79.6%，高于30%安全线"]}
07/24 00:04:10 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📡 智能止损数据已广播到前端 = {"instanceId":"chain_position_1753284004910_om4wiz","action":"HOLD","confidence":90}
07/24 00:04:10 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ 策略继续运行 = {"置信度":"90.00%","风险评分":"13.00","策略状态":"正常运行中"}
07/24 00:04:10 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-end = └──────────────────────────────────────────────────────────┘
07/24 00:04:10 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-space = 
07/24 00:04:16 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-start = ┌──────────────────────────────────────────────────────────┐
07/24 00:04:16 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-title = │              连锁头寸策略监控 [0_om4wiz] - 00:04:16              │
07/24 00:04:16 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-separator = ├──────────────────────────────────────────────────────────┤
07/24 00:04:16 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔄 新轮询周期已启动 = {"pollingCycle":45,"pollingInterval":12000,"monitoringInterval":12}
07/24 00:04:17 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 开始调用PositionAnalyticsService获取分析数据 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2,"cycleId":45}
07/24 00:04:21 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ PositionAnalyticsService数据获取成功 = {"currentPrice":0.000002994454448041206,"positionValue":7.9750686544515625,"netPnL":0.007621478625939204,"netPnLPercentage":0.09526848282424005,"activeBin":-584,"positionLowerBin":-693,"positionUpperBin":-556,"holdingDuration":0.14676111111111112}
07/24 00:04:21 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取开始 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:04:21 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:04:22 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 收益提取条件检查 = {"currentPendingYield":"0.00775581","yieldExtractionThreshold":"0.02","canExtract":false,"activeBin":-584,"positionLowerBin":-693,"positionUpperBin":-556}
07/24 00:04:22 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取完成 = {"currentPendingYield":"0.00775581","totalExtractedYield":"0.024797014174376627"}
07/24 00:04:22 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:04:22 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🏗️ 头寸重建决策结果 = {"shouldRecreate":false,"reason":"No recreation needed (智能选择: 默认状态)","recreationType":"NONE","confidence":0,"urgency":"low","recommendedAction":"Continue monitoring","hasOutOfRangeDetails":false,"timeRemaining":null}
07/24 00:04:22 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🎯 智能状态选择详情 = {"selectedReason":"No recreation needed (智能选择: 默认状态)","stateType":"NONE","confidence":0}
07/24 00:04:22 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"condition":true}
07/24 00:04:22 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"isThresholdValid":true,"currentSwitchEnabled":false,"lastSwitchUpdateTime":"2025-07-23T15:55:04.891Z"}
07/24 00:04:22 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":45,"cacheAge":1,"reason":"防止重复调用PositionAnalyticsService"}
07/24 00:04:22 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 动态重建开关数据分析 = {"benchmarkYield5Min":null,"threshold":2,"isDataValid":false,"comparisonResult":"数据无效","willUpdateSwitch":false}
07/24 00:04:22 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 进入updateDynamicRecreationSwitch方法 = {"instanceId":"chain_position_1753284004910_om4wiz","benchmarkYield5Min":null,"configThreshold":2,"currentSwitchState":false}
07/24 00:04:22 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ⚠️ 基准收益率数据无效，跳过开关状态更新 = {"benchmarkYield5Min":null,"reason":"null值"}
07/24 00:04:22 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":45,"cacheAge":2,"reason":"防止重复调用PositionAnalyticsService"}
07/24 00:04:22 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🧠 智能止损分析完成 = {"决策行动":"继续持有","置信度":"90.00%","风险评分":"13.00","紧急程度":"低","分析原因":["位置安全: 活跃bin位置79.6%，高于30%安全线"]}
07/24 00:04:22 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📡 智能止损数据已广播到前端 = {"instanceId":"chain_position_1753284004910_om4wiz","action":"HOLD","confidence":90}
07/24 00:04:22 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ 策略继续运行 = {"置信度":"90.00%","风险评分":"13.00","策略状态":"正常运行中"}
07/24 00:04:22 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-end = └──────────────────────────────────────────────────────────┘
07/24 00:04:22 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-space = 
07/24 00:04:28 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-start = ┌──────────────────────────────────────────────────────────┐
07/24 00:04:28 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-title = │              连锁头寸策略监控 [0_om4wiz] - 00:04:28              │
07/24 00:04:28 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-separator = ├──────────────────────────────────────────────────────────┤
07/24 00:04:28 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔄 新轮询周期已启动 = {"pollingCycle":46,"pollingInterval":12000,"monitoringInterval":12}
07/24 00:04:29 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 开始调用PositionAnalyticsService获取分析数据 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2,"cycleId":46}
07/24 00:04:33 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ PositionAnalyticsService数据获取成功 = {"currentPrice":0.000002994454448041206,"positionValue":7.9750686544515625,"netPnL":0.007621478625939204,"netPnLPercentage":0.09526848282424005,"activeBin":-584,"positionLowerBin":-693,"positionUpperBin":-556,"holdingDuration":0.15008972222222222}
07/24 00:04:33 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取开始 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:04:33 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:04:34 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 收益提取条件检查 = {"currentPendingYield":"0.00775581","yieldExtractionThreshold":"0.02","canExtract":false,"activeBin":-584,"positionLowerBin":-693,"positionUpperBin":-556}
07/24 00:04:34 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取完成 = {"currentPendingYield":"0.00775581","totalExtractedYield":"0.024797014174376627"}
07/24 00:04:34 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:04:34 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🏗️ 头寸重建决策结果 = {"shouldRecreate":false,"reason":"No recreation needed (智能选择: 默认状态)","recreationType":"NONE","confidence":0,"urgency":"low","recommendedAction":"Continue monitoring","hasOutOfRangeDetails":false,"timeRemaining":null}
07/24 00:04:34 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🎯 智能状态选择详情 = {"selectedReason":"No recreation needed (智能选择: 默认状态)","stateType":"NONE","confidence":0}
07/24 00:04:34 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"condition":true}
07/24 00:04:34 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"isThresholdValid":true,"currentSwitchEnabled":false,"lastSwitchUpdateTime":"2025-07-23T15:55:04.891Z"}
07/24 00:04:34 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":46,"cacheAge":2,"reason":"防止重复调用PositionAnalyticsService"}
07/24 00:04:34 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 动态重建开关数据分析 = {"benchmarkYield5Min":null,"threshold":2,"isDataValid":false,"comparisonResult":"数据无效","willUpdateSwitch":false}
07/24 00:04:34 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 进入updateDynamicRecreationSwitch方法 = {"instanceId":"chain_position_1753284004910_om4wiz","benchmarkYield5Min":null,"configThreshold":2,"currentSwitchState":false}
07/24 00:04:34 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ⚠️ 基准收益率数据无效，跳过开关状态更新 = {"benchmarkYield5Min":null,"reason":"null值"}
07/24 00:04:34 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":46,"cacheAge":3,"reason":"防止重复调用PositionAnalyticsService"}
07/24 00:04:34 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🧠 智能止损分析完成 = {"决策行动":"继续持有","置信度":"90.00%","风险评分":"12.00","紧急程度":"低","分析原因":["位置安全: 活跃bin位置79.6%，高于30%安全线"]}
07/24 00:04:34 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📡 智能止损数据已广播到前端 = {"instanceId":"chain_position_1753284004910_om4wiz","action":"HOLD","confidence":90}
07/24 00:04:34 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ 策略继续运行 = {"置信度":"90.00%","风险评分":"12.00","策略状态":"正常运行中"}
07/24 00:04:34 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-end = └──────────────────────────────────────────────────────────┘
07/24 00:04:34 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-space = 
07/24 00:04:40 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-start = ┌──────────────────────────────────────────────────────────┐
07/24 00:04:40 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-title = │              连锁头寸策略监控 [0_om4wiz] - 00:04:40              │
07/24 00:04:40 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-separator = ├──────────────────────────────────────────────────────────┤
07/24 00:04:40 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔄 新轮询周期已启动 = {"pollingCycle":47,"pollingInterval":12000,"monitoringInterval":12}
07/24 00:04:41 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 开始调用PositionAnalyticsService获取分析数据 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2,"cycleId":47}
07/24 00:04:45 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ PositionAnalyticsService数据获取成功 = {"currentPrice":0.0000029648063841992135,"positionValue":7.972505202427289,"netPnL":0.005276602173072575,"netPnLPercentage":0.06595752716340719,"activeBin":-585,"positionLowerBin":-693,"positionUpperBin":-556,"holdingDuration":0.15350027777777778}
07/24 00:04:45 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取开始 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:04:45 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:04:47 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 收益提取条件检查 = {"currentPendingYield":"0.007974385571407196","yieldExtractionThreshold":"0.02","canExtract":false,"activeBin":-585,"positionLowerBin":-693,"positionUpperBin":-556}
07/24 00:04:47 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取完成 = {"currentPendingYield":"0.007974385571407196","totalExtractedYield":"0.024797014174376627"}
07/24 00:04:47 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:04:47 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🏗️ 头寸重建决策结果 = {"shouldRecreate":false,"reason":"No recreation needed (智能选择: 默认状态)","recreationType":"NONE","confidence":0,"urgency":"low","recommendedAction":"Continue monitoring","hasOutOfRangeDetails":false,"timeRemaining":null}
07/24 00:04:47 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🎯 智能状态选择详情 = {"selectedReason":"No recreation needed (智能选择: 默认状态)","stateType":"NONE","confidence":0}
07/24 00:04:47 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"condition":true}
07/24 00:04:47 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"isThresholdValid":true,"currentSwitchEnabled":false,"lastSwitchUpdateTime":"2025-07-23T15:55:04.891Z"}
07/24 00:04:47 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":47,"cacheAge":1,"reason":"防止重复调用PositionAnalyticsService"}
07/24 00:04:47 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 动态重建开关数据分析 = {"benchmarkYield5Min":null,"threshold":2,"isDataValid":false,"comparisonResult":"数据无效","willUpdateSwitch":false}
07/24 00:04:47 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 进入updateDynamicRecreationSwitch方法 = {"instanceId":"chain_position_1753284004910_om4wiz","benchmarkYield5Min":null,"configThreshold":2,"currentSwitchState":false}
07/24 00:04:47 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ⚠️ 基准收益率数据无效，跳过开关状态更新 = {"benchmarkYield5Min":null,"reason":"null值"}
07/24 00:04:47 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":47,"cacheAge":2,"reason":"防止重复调用PositionAnalyticsService"}
07/24 00:04:47 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🧠 智能止损分析完成 = {"决策行动":"继续持有","置信度":"90.00%","风险评分":"12.00","紧急程度":"低","分析原因":["位置安全: 活跃bin位置78.8%，高于30%安全线"]}
07/24 00:04:47 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📡 智能止损数据已广播到前端 = {"instanceId":"chain_position_1753284004910_om4wiz","action":"HOLD","confidence":90}
07/24 00:04:47 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ 策略继续运行 = {"置信度":"90.00%","风险评分":"12.00","策略状态":"正常运行中"}
07/24 00:04:47 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-end = └──────────────────────────────────────────────────────────┘
07/24 00:04:47 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-space = 
07/24 00:04:52 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-start = ┌──────────────────────────────────────────────────────────┐
07/24 00:04:52 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-title = │              连锁头寸策略监控 [0_om4wiz] - 00:04:52              │
07/24 00:04:52 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-separator = ├──────────────────────────────────────────────────────────┤
07/24 00:04:52 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔄 新轮询周期已启动 = {"pollingCycle":48,"pollingInterval":12000,"monitoringInterval":12}
07/24 00:04:53 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 开始调用PositionAnalyticsService获取分析数据 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2,"cycleId":48}
07/24 00:04:55 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ PositionAnalyticsService数据获取成功 = {"currentPrice":0.000002792981727686122,"positionValue":7.957353771165231,"netPnL":-0.0033596114511018627,"netPnLPercentage":-0.04199514313877328,"activeBin":-591,"positionLowerBin":-693,"positionUpperBin":-556,"holdingDuration":0.15620277777777777}
07/24 00:04:55 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取开始 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:04:55 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:04:55 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 收益提取条件检查 = {"currentPendingYield":"0.014489603209291145","yieldExtractionThreshold":"0.02","canExtract":false,"activeBin":-591,"positionLowerBin":-693,"positionUpperBin":-556}
07/24 00:04:55 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取完成 = {"currentPendingYield":"0.014489603209291145","totalExtractedYield":"0.024797014174376627"}
07/24 00:04:55 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:04:55 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🏗️ 头寸重建决策结果 = {"shouldRecreate":false,"reason":"No recreation needed (智能选择: 默认状态)","recreationType":"NONE","confidence":0,"urgency":"low","recommendedAction":"Continue monitoring","hasOutOfRangeDetails":false,"timeRemaining":null}
07/24 00:04:55 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🎯 智能状态选择详情 = {"selectedReason":"No recreation needed (智能选择: 默认状态)","stateType":"NONE","confidence":0}
07/24 00:04:55 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"condition":true}
07/24 00:04:55 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"isThresholdValid":true,"currentSwitchEnabled":false,"lastSwitchUpdateTime":"2025-07-23T15:55:04.891Z"}
07/24 00:04:55 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":48,"cacheAge":1,"reason":"防止重复调用PositionAnalyticsService"}
07/24 00:04:55 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 动态重建开关数据分析 = {"benchmarkYield5Min":null,"threshold":2,"isDataValid":false,"comparisonResult":"数据无效","willUpdateSwitch":false}
07/24 00:04:55 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 进入updateDynamicRecreationSwitch方法 = {"instanceId":"chain_position_1753284004910_om4wiz","benchmarkYield5Min":null,"configThreshold":2,"currentSwitchState":false}
07/24 00:04:55 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ⚠️ 基准收益率数据无效，跳过开关状态更新 = {"benchmarkYield5Min":null,"reason":"null值"}
07/24 00:04:55 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":48,"cacheAge":1,"reason":"防止重复调用PositionAnalyticsService"}
07/24 00:04:55 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🧠 智能止损分析完成 = {"决策行动":"继续持有","置信度":"90.00%","风险评分":"12.99","紧急程度":"低","分析原因":["位置安全: 活跃bin位置74.5%，高于30%安全线"]}
07/24 00:04:55 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📡 智能止损数据已广播到前端 = {"instanceId":"chain_position_1753284004910_om4wiz","action":"HOLD","confidence":90}
07/24 00:04:55 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ 策略继续运行 = {"置信度":"90.00%","风险评分":"12.99","策略状态":"正常运行中"}
07/24 00:04:55 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-end = └──────────────────────────────────────────────────────────┘
07/24 00:04:55 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-space = 
07/24 00:05:04 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-start = ┌──────────────────────────────────────────────────────────┐
07/24 00:05:04 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-title = │              连锁头寸策略监控 [0_om4wiz] - 00:05:04              │
07/24 00:05:04 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-separator = ├──────────────────────────────────────────────────────────┤
07/24 00:05:04 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔄 新轮询周期已启动 = {"pollingCycle":49,"pollingInterval":12000,"monitoringInterval":12}
07/24 00:05:06 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 开始调用PositionAnalyticsService获取分析数据 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2,"cycleId":49}
07/24 00:05:09 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ PositionAnalyticsService数据获取成功 = {"currentPrice":0.000002792981727686122,"positionValue":7.953737994824527,"netPnL":-0.0014541977918050009,"netPnLPercentage":-0.01817747239756251,"activeBin":-591,"positionLowerBin":-693,"positionUpperBin":-556,"holdingDuration":0.160175}
07/24 00:05:09 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取开始 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:05:09 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:05:09 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 收益提取条件检查 = {"currentPendingYield":"0.020010793209291146","yieldExtractionThreshold":"0.02","canExtract":true,"activeBin":-591,"positionLowerBin":-693,"positionUpperBin":-556}
07/24 00:05:16 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-start = ┌──────────────────────────────────────────────────────────┐
07/24 00:05:16 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-title = │              连锁头寸策略监控 [0_om4wiz] - 00:05:16              │
07/24 00:05:16 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-separator = ├──────────────────────────────────────────────────────────┤
07/24 00:05:16 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔄 新轮询周期已启动 = {"pollingCycle":50,"pollingInterval":12000,"monitoringInterval":12}
07/24 00:05:17 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 开始调用PositionAnalyticsService获取分析数据 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2,"cycleId":50}
07/24 00:05:22 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ PositionAnalyticsService数据获取成功 = {"currentPrice":0.000002994454448041206,"positionValue":7.9772109101621655,"netPnL":0.042515255299292676,"netPnLPercentage":0.5314406912411584,"activeBin":-584,"positionLowerBin":-693,"positionUpperBin":-556,"holdingDuration":0.16370083333333332}
07/24 00:05:22 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取开始 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:05:22 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:05:22 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 收益提取条件检查 = {"currentPendingYield":"0.020496537753459155","yieldExtractionThreshold":"0.02","canExtract":true,"activeBin":-584,"positionLowerBin":-693,"positionUpperBin":-556}
07/24 00:05:23 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取完成 = {"currentPendingYield":"0.020496537753459155","totalExtractedYield":"0.04480780738366777"}
07/24 00:05:23 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:05:23 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🏗️ 头寸重建决策结果 = {"shouldRecreate":false,"reason":"No recreation needed (智能选择: 默认状态)","recreationType":"NONE","confidence":0,"urgency":"low","recommendedAction":"Continue monitoring","hasOutOfRangeDetails":false,"timeRemaining":null}
07/24 00:05:23 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🎯 智能状态选择详情 = {"selectedReason":"No recreation needed (智能选择: 默认状态)","stateType":"NONE","confidence":0}
07/24 00:05:23 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"condition":true}
07/24 00:05:23 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"isThresholdValid":true,"currentSwitchEnabled":false,"lastSwitchUpdateTime":"2025-07-23T15:55:04.891Z"}
07/24 00:05:23 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":50,"cacheAge":3,"reason":"防止重复调用PositionAnalyticsService"}
07/24 00:05:23 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 动态重建开关数据分析 = {"benchmarkYield5Min":null,"threshold":2,"isDataValid":false,"comparisonResult":"数据无效","willUpdateSwitch":false}
07/24 00:05:23 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 进入updateDynamicRecreationSwitch方法 = {"instanceId":"chain_position_1753284004910_om4wiz","benchmarkYield5Min":null,"configThreshold":2,"currentSwitchState":false}
07/24 00:05:23 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ⚠️ 基准收益率数据无效，跳过开关状态更新 = {"benchmarkYield5Min":null,"reason":"null值"}
07/24 00:05:23 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":50,"cacheAge":6,"reason":"防止重复调用PositionAnalyticsService"}
07/24 00:05:23 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🧠 智能止损分析完成 = {"决策行动":"继续持有","置信度":"90.00%","风险评分":"12.00","紧急程度":"低","分析原因":["位置安全: 活跃bin位置79.6%，高于30%安全线"]}
07/24 00:05:23 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📡 智能止损数据已广播到前端 = {"instanceId":"chain_position_1753284004910_om4wiz","action":"HOLD","confidence":90}
07/24 00:05:23 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ 策略继续运行 = {"置信度":"90.00%","风险评分":"12.00","策略状态":"正常运行中"}
07/24 00:05:23 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-end = └──────────────────────────────────────────────────────────┘
07/24 00:05:23 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-space = 
07/24 00:05:24 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取完成 = {"currentPendingYield":"0.020010793209291146","totalExtractedYield":"0.024797014174376627"}
07/24 00:05:24 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:05:24 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🏗️ 头寸重建决策结果 = {"shouldRecreate":false,"reason":"No recreation needed (智能选择: 默认状态)","recreationType":"NONE","confidence":0,"urgency":"low","recommendedAction":"Continue monitoring","hasOutOfRangeDetails":false,"timeRemaining":null}
07/24 00:05:24 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🎯 智能状态选择详情 = {"selectedReason":"No recreation needed (智能选择: 默认状态)","stateType":"NONE","confidence":0}
07/24 00:05:24 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"condition":true}
07/24 00:05:24 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"isThresholdValid":true,"currentSwitchEnabled":false,"lastSwitchUpdateTime":"2025-07-23T15:55:04.891Z"}
07/24 00:05:24 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 开始调用PositionAnalyticsService获取分析数据 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2,"cycleId":50}
07/24 00:05:24 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ PositionAnalyticsService数据获取成功 = {"currentPrice":0.000002994454448041206,"positionValue":7.9772109101621655,"netPnL":0.042515255299292676,"netPnLPercentage":0.5314406912411584,"activeBin":-584,"positionLowerBin":-693,"positionUpperBin":-556,"holdingDuration":0.164315}
07/24 00:05:24 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取开始 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:05:24 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:05:24 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 收益提取条件检查 = {"currentPendingYield":"0.020496537753459155","yieldExtractionThreshold":"0.02","canExtract":true,"activeBin":-584,"positionLowerBin":-693,"positionUpperBin":-556}
07/24 00:05:24 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取完成 = {"currentPendingYield":"0.020496537753459155","totalExtractedYield":"0.04480780738366777"}
07/24 00:05:24 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:05:24 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 动态重建开关数据分析 = {"benchmarkYield5Min":null,"threshold":2,"isDataValid":false,"comparisonResult":"数据无效","willUpdateSwitch":false}
07/24 00:05:24 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 进入updateDynamicRecreationSwitch方法 = {"instanceId":"chain_position_1753284004910_om4wiz","benchmarkYield5Min":null,"configThreshold":2,"currentSwitchState":false}
07/24 00:05:24 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ⚠️ 基准收益率数据无效，跳过开关状态更新 = {"benchmarkYield5Min":null,"reason":"null值"}
07/24 00:05:24 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":50,"cacheAge":1,"reason":"防止重复调用PositionAnalyticsService"}
07/24 00:05:24 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🧠 智能止损分析完成 = {"决策行动":"继续持有","置信度":"90.00%","风险评分":"12.00","紧急程度":"低","分析原因":["位置安全: 活跃bin位置79.6%，高于30%安全线"]}
07/24 00:05:24 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📡 智能止损数据已广播到前端 = {"instanceId":"chain_position_1753284004910_om4wiz","action":"HOLD","confidence":90}
07/24 00:05:24 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ 策略继续运行 = {"置信度":"90.00%","风险评分":"12.00","策略状态":"正常运行中"}
07/24 00:05:24 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-end = └──────────────────────────────────────────────────────────┘
07/24 00:05:24 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-space = 
07/24 00:05:28 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-start = ┌──────────────────────────────────────────────────────────┐
07/24 00:05:28 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-title = │              连锁头寸策略监控 [0_om4wiz] - 00:05:28              │
07/24 00:05:28 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-separator = ├──────────────────────────────────────────────────────────┤
07/24 00:05:28 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔄 新轮询周期已启动 = {"pollingCycle":51,"pollingInterval":12000,"monitoringInterval":12}
07/24 00:05:29 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 开始调用PositionAnalyticsService获取分析数据 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2,"cycleId":51}
07/24 00:05:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ PositionAnalyticsService数据获取成功 = {"currentPrice":0.000002994454448041206,"positionValue":7.975069453107286,"netPnL":0.019877260490954285,"netPnLPercentage":0.24846575613692856,"activeBin":-584,"positionLowerBin":-693,"positionUpperBin":-556,"holdingDuration":0.16642083333333332}
07/24 00:05:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取开始 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:05:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:05:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 收益提取条件检查 = {"currentPendingYield":"0","yieldExtractionThreshold":"0.02","canExtract":false,"activeBin":-584,"positionLowerBin":-693,"positionUpperBin":-556}
07/24 00:05:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取完成 = {"currentPendingYield":"0","totalExtractedYield":"0.04480780738366777"}
07/24 00:05:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:05:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🏗️ 头寸重建决策结果 = {"shouldRecreate":false,"reason":"No recreation needed (智能选择: 默认状态)","recreationType":"NONE","confidence":0,"urgency":"low","recommendedAction":"Continue monitoring","hasOutOfRangeDetails":false,"timeRemaining":null}
07/24 00:05:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🎯 智能状态选择详情 = {"selectedReason":"No recreation needed (智能选择: 默认状态)","stateType":"NONE","confidence":0}
07/24 00:05:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"condition":true}
07/24 00:05:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"isThresholdValid":true,"currentSwitchEnabled":false,"lastSwitchUpdateTime":"2025-07-23T15:55:04.891Z"}
07/24 00:05:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":51,"cacheAge":1,"reason":"防止重复调用PositionAnalyticsService"}
07/24 00:05:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 动态重建开关数据分析 = {"benchmarkYield5Min":null,"threshold":2,"isDataValid":false,"comparisonResult":"数据无效","willUpdateSwitch":false}
07/24 00:05:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 进入updateDynamicRecreationSwitch方法 = {"instanceId":"chain_position_1753284004910_om4wiz","benchmarkYield5Min":null,"configThreshold":2,"currentSwitchState":false}
07/24 00:05:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ⚠️ 基准收益率数据无效，跳过开关状态更新 = {"benchmarkYield5Min":null,"reason":"null值"}
07/24 00:05:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":51,"cacheAge":1,"reason":"防止重复调用PositionAnalyticsService"}
07/24 00:05:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🧠 智能止损分析完成 = {"决策行动":"继续持有","置信度":"90.00%","风险评分":"35.21","紧急程度":"低","分析原因":["位置安全: 活跃bin位置79.6%，高于30%安全线"]}
07/24 00:05:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📡 智能止损数据已广播到前端 = {"instanceId":"chain_position_1753284004910_om4wiz","action":"HOLD","confidence":90}
07/24 00:05:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ 策略继续运行 = {"置信度":"90.00%","风险评分":"35.21","策略状态":"正常运行中"}
07/24 00:05:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-end = └──────────────────────────────────────────────────────────┘
07/24 00:05:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-space = 
07/24 00:05:40 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-start = ┌──────────────────────────────────────────────────────────┐
07/24 00:05:40 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-title = │              连锁头寸策略监控 [0_om4wiz] - 00:05:40              │
07/24 00:05:40 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-separator = ├──────────────────────────────────────────────────────────┤
07/24 00:05:40 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔄 新轮询周期已启动 = {"pollingCycle":52,"pollingInterval":12000,"monitoringInterval":12}
07/24 00:05:41 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 开始调用PositionAnalyticsService获取分析数据 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2,"cycleId":52}
07/24 00:05:46 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ PositionAnalyticsService数据获取成功 = {"currentPrice":0.000002994454448041206,"positionValue":7.975069453107286,"netPnL":0.021598710490954076,"netPnLPercentage":0.26998388113692595,"activeBin":-584,"positionLowerBin":-693,"positionUpperBin":-556,"holdingDuration":0.17036583333333333}
07/24 00:05:46 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取开始 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:05:46 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:05:46 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 收益提取条件检查 = {"currentPendingYield":"0.00172145","yieldExtractionThreshold":"0.02","canExtract":false,"activeBin":-584,"positionLowerBin":-693,"positionUpperBin":-556}
07/24 00:05:46 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取完成 = {"currentPendingYield":"0.00172145","totalExtractedYield":"0.04480780738366777"}
07/24 00:05:46 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:05:46 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🏗️ 头寸重建决策结果 = {"shouldRecreate":false,"reason":"No recreation needed (智能选择: 默认状态)","recreationType":"NONE","confidence":0,"urgency":"low","recommendedAction":"Continue monitoring","hasOutOfRangeDetails":false,"timeRemaining":null}
07/24 00:05:46 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🎯 智能状态选择详情 = {"selectedReason":"No recreation needed (智能选择: 默认状态)","stateType":"NONE","confidence":0}
07/24 00:05:46 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"condition":true}
07/24 00:05:46 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"isThresholdValid":true,"currentSwitchEnabled":false,"lastSwitchUpdateTime":"2025-07-23T15:55:04.891Z"}
07/24 00:05:46 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":52,"cacheAge":1,"reason":"防止重复调用PositionAnalyticsService"}
07/24 00:05:46 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 动态重建开关数据分析 = {"benchmarkYield5Min":null,"threshold":2,"isDataValid":false,"comparisonResult":"数据无效","willUpdateSwitch":false}
07/24 00:05:46 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 进入updateDynamicRecreationSwitch方法 = {"instanceId":"chain_position_1753284004910_om4wiz","benchmarkYield5Min":null,"configThreshold":2,"currentSwitchState":false}
07/24 00:05:46 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ⚠️ 基准收益率数据无效，跳过开关状态更新 = {"benchmarkYield5Min":null,"reason":"null值"}
07/24 00:05:46 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":52,"cacheAge":2,"reason":"防止重复调用PositionAnalyticsService"}
07/24 00:05:46 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🧠 智能止损分析完成 = {"决策行动":"继续持有","置信度":"90.00%","风险评分":"28.00","紧急程度":"低","分析原因":["位置安全: 活跃bin位置79.6%，高于30%安全线"]}
07/24 00:05:46 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📡 智能止损数据已广播到前端 = {"instanceId":"chain_position_1753284004910_om4wiz","action":"HOLD","confidence":90}
07/24 00:05:46 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ 策略继续运行 = {"置信度":"90.00%","风险评分":"28.00","策略状态":"正常运行中"}
07/24 00:05:46 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-end = └──────────────────────────────────────────────────────────┘
07/24 00:05:46 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-space = 
07/24 00:05:52 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-start = ┌──────────────────────────────────────────────────────────┐
07/24 00:05:52 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-title = │              连锁头寸策略监控 [0_om4wiz] - 00:05:52              │
07/24 00:05:52 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-separator = ├──────────────────────────────────────────────────────────┤
07/24 00:05:52 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔄 新轮询周期已启动 = {"pollingCycle":53,"pollingInterval":12000,"monitoringInterval":12}
07/24 00:05:53 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 开始调用PositionAnalyticsService获取分析数据 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2,"cycleId":53}
07/24 00:05:54 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ PositionAnalyticsService数据获取成功 = {"currentPrice":0.000003024398992521618,"positionValue":7.97770030763836,"netPnL":0.024229565022027444,"netPnLPercentage":0.30286956277534305,"activeBin":-583,"positionLowerBin":-693,"positionUpperBin":-556,"holdingDuration":0.17251277777777776}
07/24 00:05:54 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取开始 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:05:54 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:05:54 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 收益提取条件检查 = {"currentPendingYield":"0.00172145","yieldExtractionThreshold":"0.02","canExtract":false,"activeBin":-583,"positionLowerBin":-693,"positionUpperBin":-556}
07/24 00:05:54 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取完成 = {"currentPendingYield":"0.00172145","totalExtractedYield":"0.04480780738366777"}
07/24 00:05:54 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:05:54 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🏗️ 头寸重建决策结果 = {"shouldRecreate":false,"reason":"No recreation needed (智能选择: 默认状态)","recreationType":"NONE","confidence":0,"urgency":"low","recommendedAction":"Continue monitoring","hasOutOfRangeDetails":false,"timeRemaining":null}
07/24 00:05:54 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🎯 智能状态选择详情 = {"selectedReason":"No recreation needed (智能选择: 默认状态)","stateType":"NONE","confidence":0}
07/24 00:05:54 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"condition":true}
07/24 00:05:54 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"isThresholdValid":true,"currentSwitchEnabled":false,"lastSwitchUpdateTime":"2025-07-23T15:55:04.891Z"}
07/24 00:05:54 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":53,"cacheAge":1,"reason":"防止重复调用PositionAnalyticsService"}
07/24 00:05:54 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 动态重建开关数据分析 = {"benchmarkYield5Min":null,"threshold":2,"isDataValid":false,"comparisonResult":"数据无效","willUpdateSwitch":false}
07/24 00:05:54 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 进入updateDynamicRecreationSwitch方法 = {"instanceId":"chain_position_1753284004910_om4wiz","benchmarkYield5Min":null,"configThreshold":2,"currentSwitchState":false}
07/24 00:05:54 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ⚠️ 基准收益率数据无效，跳过开关状态更新 = {"benchmarkYield5Min":null,"reason":"null值"}
07/24 00:05:54 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":53,"cacheAge":2,"reason":"防止重复调用PositionAnalyticsService"}
07/24 00:05:54 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🧠 智能止损分析完成 = {"决策行动":"继续持有","置信度":"90.00%","风险评分":"28.00","紧急程度":"低","分析原因":["位置安全: 活跃bin位置80.3%，高于30%安全线"]}
07/24 00:05:54 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📡 智能止损数据已广播到前端 = {"instanceId":"chain_position_1753284004910_om4wiz","action":"HOLD","confidence":90}
07/24 00:05:54 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ 策略继续运行 = {"置信度":"90.00%","风险评分":"28.00","策略状态":"正常运行中"}
07/24 00:05:54 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-end = └──────────────────────────────────────────────────────────┘
07/24 00:05:54 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-space = 
07/24 00:06:04 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-start = ┌──────────────────────────────────────────────────────────┐
07/24 00:06:04 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-title = │              连锁头寸策略监控 [0_om4wiz] - 00:06:04              │
07/24 00:06:04 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-separator = ├──────────────────────────────────────────────────────────┤
07/24 00:06:04 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔄 新轮询周期已启动 = {"pollingCycle":54,"pollingInterval":12000,"monitoringInterval":12}
07/24 00:06:05 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 开始调用PositionAnalyticsService获取分析数据 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2,"cycleId":54}
07/24 00:06:08 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ PositionAnalyticsService数据获取成功 = {"currentPrice":0.0000031160413063940156,"positionValue":7.983885139577185,"netPnL":0.03317617696085229,"netPnLPercentage":0.4147022120106536,"activeBin":-580,"positionLowerBin":-693,"positionUpperBin":-556,"holdingDuration":0.17646722222222222}
07/24 00:06:08 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取开始 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:06:08 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:06:08 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 收益提取条件检查 = {"currentPendingYield":"0.00448323","yieldExtractionThreshold":"0.02","canExtract":false,"activeBin":-580,"positionLowerBin":-693,"positionUpperBin":-556}
07/24 00:06:08 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取完成 = {"currentPendingYield":"0.00448323","totalExtractedYield":"0.04480780738366777"}
07/24 00:06:08 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:06:08 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🏗️ 头寸重建决策结果 = {"shouldRecreate":false,"reason":"No recreation needed (智能选择: 默认状态)","recreationType":"NONE","confidence":0,"urgency":"low","recommendedAction":"Continue monitoring","hasOutOfRangeDetails":false,"timeRemaining":null}
07/24 00:06:08 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🎯 智能状态选择详情 = {"selectedReason":"No recreation needed (智能选择: 默认状态)","stateType":"NONE","confidence":0}
07/24 00:06:08 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"condition":true}
07/24 00:06:08 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"isThresholdValid":true,"currentSwitchEnabled":false,"lastSwitchUpdateTime":"2025-07-23T15:55:04.891Z"}
07/24 00:06:08 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":54,"cacheAge":1,"reason":"防止重复调用PositionAnalyticsService"}
07/24 00:06:08 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 动态重建开关数据分析 = {"benchmarkYield5Min":null,"threshold":2,"isDataValid":false,"comparisonResult":"数据无效","willUpdateSwitch":false}
07/24 00:06:08 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 进入updateDynamicRecreationSwitch方法 = {"instanceId":"chain_position_1753284004910_om4wiz","benchmarkYield5Min":null,"configThreshold":2,"currentSwitchState":false}
07/24 00:06:08 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ⚠️ 基准收益率数据无效，跳过开关状态更新 = {"benchmarkYield5Min":null,"reason":"null值"}
07/24 00:06:08 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":54,"cacheAge":3,"reason":"防止重复调用PositionAnalyticsService"}
07/24 00:06:08 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🧠 智能止损分析完成 = {"决策行动":"继续持有","置信度":"90.00%","风险评分":"29.00","紧急程度":"低","分析原因":["位置安全: 活跃bin位置82.5%，高于30%安全线"]}
07/24 00:06:08 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📡 智能止损数据已广播到前端 = {"instanceId":"chain_position_1753284004910_om4wiz","action":"HOLD","confidence":90}
07/24 00:06:08 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ 策略继续运行 = {"置信度":"90.00%","风险评分":"29.00","策略状态":"正常运行中"}
07/24 00:06:08 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-end = └──────────────────────────────────────────────────────────┘
07/24 00:06:08 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-space = 
07/24 00:06:16 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-start = ┌──────────────────────────────────────────────────────────┐
07/24 00:06:16 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-title = │              连锁头寸策略监控 [0_om4wiz] - 00:06:16              │
07/24 00:06:16 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-separator = ├──────────────────────────────────────────────────────────┤
07/24 00:06:16 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔄 新轮询周期已启动 = {"pollingCycle":55,"pollingInterval":12000,"monitoringInterval":12}
07/24 00:06:17 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 开始调用PositionAnalyticsService获取分析数据 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2,"cycleId":55}
07/24 00:06:20 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ PositionAnalyticsService数据获取成功 = {"currentPrice":0.0000031160413063940156,"positionValue":7.983885139577185,"netPnL":0.03317617696085229,"netPnLPercentage":0.4147022120106536,"activeBin":-580,"positionLowerBin":-693,"positionUpperBin":-556,"holdingDuration":0.17996222222222222}
07/24 00:06:20 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取开始 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:06:20 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:06:20 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 收益提取条件检查 = {"currentPendingYield":"0.00448323","yieldExtractionThreshold":"0.02","canExtract":false,"activeBin":-580,"positionLowerBin":-693,"positionUpperBin":-556}
07/24 00:06:20 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取完成 = {"currentPendingYield":"0.00448323","totalExtractedYield":"0.04480780738366777"}
07/24 00:06:20 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:06:20 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🏗️ 头寸重建决策结果 = {"shouldRecreate":false,"reason":"No recreation needed (智能选择: 默认状态)","recreationType":"NONE","confidence":0,"urgency":"low","recommendedAction":"Continue monitoring","hasOutOfRangeDetails":false,"timeRemaining":null}
07/24 00:06:20 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🎯 智能状态选择详情 = {"selectedReason":"No recreation needed (智能选择: 默认状态)","stateType":"NONE","confidence":0}
07/24 00:06:20 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"condition":true}
07/24 00:06:20 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"isThresholdValid":true,"currentSwitchEnabled":false,"lastSwitchUpdateTime":"2025-07-23T15:55:04.891Z"}
07/24 00:06:20 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":55,"cacheAge":1,"reason":"防止重复调用PositionAnalyticsService"}
07/24 00:06:20 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 动态重建开关数据分析 = {"benchmarkYield5Min":null,"threshold":2,"isDataValid":false,"comparisonResult":"数据无效","willUpdateSwitch":false}
07/24 00:06:20 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 进入updateDynamicRecreationSwitch方法 = {"instanceId":"chain_position_1753284004910_om4wiz","benchmarkYield5Min":null,"configThreshold":2,"currentSwitchState":false}
07/24 00:06:20 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ⚠️ 基准收益率数据无效，跳过开关状态更新 = {"benchmarkYield5Min":null,"reason":"null值"}
07/24 00:06:20 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":55,"cacheAge":2,"reason":"防止重复调用PositionAnalyticsService"}
07/24 00:06:20 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🧠 智能止损分析完成 = {"决策行动":"继续持有","置信度":"90.00%","风险评分":"17.03","紧急程度":"低","分析原因":["位置安全: 活跃bin位置82.5%，高于30%安全线"]}
07/24 00:06:20 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📡 智能止损数据已广播到前端 = {"instanceId":"chain_position_1753284004910_om4wiz","action":"HOLD","confidence":90}
07/24 00:06:20 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ 策略继续运行 = {"置信度":"90.00%","风险评分":"17.03","策略状态":"正常运行中"}
07/24 00:06:20 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-end = └──────────────────────────────────────────────────────────┘
07/24 00:06:20 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-space = 
07/24 00:06:28 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-start = ┌──────────────────────────────────────────────────────────┐
07/24 00:06:28 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-title = │              连锁头寸策略监控 [0_om4wiz] - 00:06:28              │
07/24 00:06:28 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-separator = ├──────────────────────────────────────────────────────────┤
07/24 00:06:28 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔄 新轮询周期已启动 = {"pollingCycle":56,"pollingInterval":12000,"monitoringInterval":12}
07/24 00:06:29 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 开始调用PositionAnalyticsService获取分析数据 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2,"cycleId":56}
07/24 00:06:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ PositionAnalyticsService数据获取成功 = {"currentPrice":0.0000031160413063940156,"positionValue":7.983885139577185,"netPnL":0.03317617696085229,"netPnLPercentage":0.4147022120106536,"activeBin":-580,"positionLowerBin":-693,"positionUpperBin":-556,"holdingDuration":0.18323861111111112}
07/24 00:06:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取开始 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:06:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:06:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 收益提取条件检查 = {"currentPendingYield":"0.00448323","yieldExtractionThreshold":"0.02","canExtract":false,"activeBin":-580,"positionLowerBin":-693,"positionUpperBin":-556}
07/24 00:06:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取完成 = {"currentPendingYield":"0.00448323","totalExtractedYield":"0.04480780738366777"}
07/24 00:06:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:06:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🏗️ 头寸重建决策结果 = {"shouldRecreate":false,"reason":"No recreation needed (智能选择: 默认状态)","recreationType":"NONE","confidence":0,"urgency":"low","recommendedAction":"Continue monitoring","hasOutOfRangeDetails":false,"timeRemaining":null}
07/24 00:06:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🎯 智能状态选择详情 = {"selectedReason":"No recreation needed (智能选择: 默认状态)","stateType":"NONE","confidence":0}
07/24 00:06:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"condition":true}
07/24 00:06:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"isThresholdValid":true,"currentSwitchEnabled":false,"lastSwitchUpdateTime":"2025-07-23T15:55:04.891Z"}
07/24 00:06:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":56,"cacheAge":0,"reason":"防止重复调用PositionAnalyticsService"}
07/24 00:06:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 动态重建开关数据分析 = {"benchmarkYield5Min":null,"threshold":2,"isDataValid":false,"comparisonResult":"数据无效","willUpdateSwitch":false}
07/24 00:06:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 进入updateDynamicRecreationSwitch方法 = {"instanceId":"chain_position_1753284004910_om4wiz","benchmarkYield5Min":null,"configThreshold":2,"currentSwitchState":false}
07/24 00:06:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ⚠️ 基准收益率数据无效，跳过开关状态更新 = {"benchmarkYield5Min":null,"reason":"null值"}
07/24 00:06:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":56,"cacheAge":1,"reason":"防止重复调用PositionAnalyticsService"}
07/24 00:06:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🧠 智能止损分析完成 = {"决策行动":"继续持有","置信度":"90.00%","风险评分":"12.00","紧急程度":"低","分析原因":["位置安全: 活跃bin位置82.5%，高于30%安全线"]}
07/24 00:06:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📡 智能止损数据已广播到前端 = {"instanceId":"chain_position_1753284004910_om4wiz","action":"HOLD","confidence":90}
07/24 00:06:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ 策略继续运行 = {"置信度":"90.00%","风险评分":"12.00","策略状态":"正常运行中"}
07/24 00:06:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-end = └──────────────────────────────────────────────────────────┘
07/24 00:06:32 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-space = 
07/24 00:06:40 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-start = ┌──────────────────────────────────────────────────────────┐
07/24 00:06:40 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-title = │              连锁头寸策略监控 [0_om4wiz] - 00:06:40              │
07/24 00:06:40 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-separator = ├──────────────────────────────────────────────────────────┤
07/24 00:06:40 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔄 新轮询周期已启动 = {"pollingCycle":57,"pollingInterval":12000,"monitoringInterval":12}
07/24 00:06:41 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 开始调用PositionAnalyticsService获取分析数据 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2,"cycleId":57}
07/24 00:06:44 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ PositionAnalyticsService数据获取成功 = {"currentPrice":0.0000031160413063940156,"positionValue":7.983885139580302,"netPnL":0.033176176963969795,"netPnLPercentage":0.41470221204962243,"activeBin":-580,"positionLowerBin":-693,"positionUpperBin":-556,"holdingDuration":0.18664972222222223}
07/24 00:06:44 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取开始 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:06:44 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:06:44 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 收益提取条件检查 = {"currentPendingYield":"0.00448323","yieldExtractionThreshold":"0.02","canExtract":false,"activeBin":-580,"positionLowerBin":-693,"positionUpperBin":-556}
07/24 00:06:44 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 收益数据获取完成 = {"currentPendingYield":"0.00448323","totalExtractedYield":"0.04480780738366777"}
07/24 00:06:44 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 开始获取完整分析报告 - 使用统一数据流 = {"poolAddress":"5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF","positionCount":2}
07/24 00:06:44 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🏗️ 头寸重建决策结果 = {"shouldRecreate":false,"reason":"No recreation needed (智能选择: 默认状态)","recreationType":"NONE","confidence":0,"urgency":"low","recommendedAction":"Continue monitoring","hasOutOfRangeDetails":false,"timeRemaining":null}
07/24 00:06:44 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🎯 智能状态选择详情 = {"selectedReason":"No recreation needed (智能选择: 默认状态)","stateType":"NONE","confidence":0}
07/24 00:06:44 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"condition":true}
07/24 00:06:44 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 检查动态重建开关配置 = {"benchmarkYieldThreshold5Min":2,"isThresholdValid":true,"currentSwitchEnabled":false,"lastSwitchUpdateTime":"2025-07-23T15:55:04.891Z"}
07/24 00:06:44 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":57,"cacheAge":2,"reason":"防止重复调用PositionAnalyticsService"}
07/24 00:06:44 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 动态重建开关数据分析 = {"benchmarkYield5Min":null,"threshold":2,"isDataValid":false,"comparisonResult":"数据无效","willUpdateSwitch":false}
07/24 00:06:44 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🔍 进入updateDynamicRecreationSwitch方法 = {"instanceId":"chain_position_1753284004910_om4wiz","benchmarkYield5Min":null,"configThreshold":2,"currentSwitchState":false}
07/24 00:06:44 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ⚠️ 基准收益率数据无效，跳过开关状态更新 = {"benchmarkYield5Min":null,"reason":"null值"}
07/24 00:06:44 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📊 使用缓存的市场数据 = {"cycleId":57,"cacheAge":4,"reason":"防止重复调用PositionAnalyticsService"}
07/24 00:06:44 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 🧠 智能止损分析完成 = {"决策行动":"继续持有","置信度":"90.00%","风险评分":"12.00","紧急程度":"低","分析原因":["位置安全: 活跃bin位置82.5%，高于30%安全线"]}
07/24 00:06:44 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: 📡 智能止损数据已广播到前端 = {"instanceId":"chain_position_1753284004910_om4wiz","action":"HOLD","confidence":90}
07/24 00:06:44 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: ✅ 策略继续运行 = {"置信度":"90.00%","风险评分":"12.00","策略状态":"正常运行中"}
07/24 00:06:44 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-end = └──────────────────────────────────────────────────────────┘
07/24 00:06:44 INFO [strategy-chain_position_1753284004910_om4wiz] MONITOR: monitoring-frame-space = 
