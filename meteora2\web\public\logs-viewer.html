<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>策略日志仪表盘 - DLMM流动性管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
            color: #e2e8f0;
            height: 100vh;
            overflow: hidden;
        }

        .dashboard {
            display: flex;
            height: 100vh;
        }

        /* 左侧栏 - 策略实例列表 */
        .sidebar {
            width: 320px;
            background: rgba(30, 41, 59, 0.95);
            backdrop-filter: blur(10px);
            border-right: 1px solid rgba(71, 85, 105, 0.3);
            box-shadow: 2px 0 20px rgba(0, 0, 0, 0.3);
            display: flex;
            flex-direction: column;
        }

        .sidebar-header {
            padding: 20px;
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            color: white;
            text-align: center;
        }

        .sidebar-header h1 {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .sidebar-header .subtitle {
            font-size: 12px;
            opacity: 0.9;
        }

        .sidebar-stats {
            padding: 15px 20px;
            background: rgba(59, 130, 246, 0.15);
            border-bottom: 1px solid rgba(71, 85, 105, 0.3);
        }

        .stats-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
            font-size: 14px;
        }

        .stats-item:last-child {
            margin-bottom: 0;
        }

        .stats-label {
            color: #94a3b8;
        }

        .stats-value {
            font-weight: 600;
            color: #60a5fa;
        }

        .instance-list {
            flex: 1;
            overflow-y: auto;
            padding: 10px;
        }

        .instance-item {
            margin-bottom: 8px;
            border-radius: 8px;
            overflow: hidden;
            background: rgba(51, 65, 85, 0.8);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .instance-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.4);
            background: rgba(71, 85, 105, 0.9);
        }

        .instance-item.active {
            border-color: #60a5fa;
            box-shadow: 0 4px 16px rgba(96, 165, 250, 0.4);
            background: rgba(59, 130, 246, 0.2);
        }

        .instance-header {
            padding: 12px 15px;
            background: rgba(30, 41, 59, 0.5);
        }

        .instance-id {
            font-weight: 600;
            font-size: 13px;
            color: #e2e8f0;
            margin-bottom: 4px;
            word-break: break-all;
        }

        .instance-meta {
            display: flex;
            justify-content: space-between;
            font-size: 11px;
            color: #94a3b8;
        }

        .instance-status {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .status-dot {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background: #10b981;
        }

        /* 系统日志和策略日志的区别样式 */
        .instance-item.system-log {
            border-left: 3px solid #f59e0b;
        }

        .instance-item.system-log .status-dot {
            background: #f59e0b;
        }

        .instance-item.system-log:hover {
            background: rgba(245, 158, 11, 0.1);
        }

        .instance-item.system-log.active {
            border-color: #f59e0b;
            box-shadow: 0 4px 16px rgba(245, 158, 11, 0.4);
            background: rgba(245, 158, 11, 0.2);
        }

        .instance-item.strategy-log {
            border-left: 3px solid transparent;
        }

        /* 🔧 简单Y策略样式 */
        .instance-item.simple-y-log {
            border-left: 3px solid #10b981;
        }

        .instance-item.simple-y-log .status-dot {
            background: #10b981;
        }

        .instance-item.simple-y-log:hover {
            background: rgba(16, 185, 129, 0.1);
        }

        .instance-item.simple-y-log.active {
            border-color: #10b981;
            box-shadow: 0 4px 16px rgba(16, 185, 129, 0.4);
            background: rgba(16, 185, 129, 0.2);
        }

        /* 🔧 连锁头寸策略样式 */
        .instance-item.chain-position-log {
            border-left: 3px solid #3b82f6;
        }

        .instance-item.chain-position-log .status-dot {
            background: #3b82f6;
        }

        .instance-item.chain-position-log:hover {
            background: rgba(59, 130, 246, 0.1);
        }

        .instance-item.chain-position-log.active {
            border-color: #3b82f6;
            box-shadow: 0 4px 16px rgba(59, 130, 246, 0.4);
            background: rgba(59, 130, 246, 0.2);
        }

        /* 右侧主内容区 */
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: rgba(15, 23, 42, 0.9);
            backdrop-filter: blur(10px);
        }

        .main-header {
            padding: 20px 30px;
            background: rgba(30, 41, 59, 0.95);
            border-bottom: 1px solid rgba(71, 85, 105, 0.3);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
        }

        .main-title {
            font-size: 24px;
            font-weight: 600;
            color: #f1f5f9;
            margin-bottom: 8px;
        }

        .main-subtitle {
            color: #94a3b8;
            font-size: 14px;
        }

        .log-controls {
            padding: 15px 30px;
            background: rgba(51, 65, 85, 0.8);
            border-bottom: 1px solid rgba(71, 85, 105, 0.3);
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .control-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .control-label {
            font-size: 14px;
            color: #e2e8f0;
            font-weight: 500;
        }

        .control-input {
            padding: 6px 12px;
            border: 1px solid #475569;
            border-radius: 4px;
            font-size: 14px;
            background: rgba(30, 41, 59, 0.8);
            color: #e2e8f0;
            transition: all 0.3s ease;
        }

        .control-input:focus {
            outline: none;
            border-color: #60a5fa;
            box-shadow: 0 0 0 2px rgba(96, 165, 250, 0.2);
        }

        .control-input::placeholder {
            color: #94a3b8;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
        }

        .btn-secondary {
            background: #64748b;
            color: white;
        }

        .btn-secondary:hover {
            background: #475569;
        }

        .btn-danger {
            background: #ef4444;
            color: white;
        }

        .btn-danger:hover {
            background: #dc2626;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(239, 68, 68, 0.4);
        }

        .btn-filter-active {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
        }

        .btn-filter-active:hover {
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
        }

        .log-container {
            flex: 1;
            padding: 20px 30px;
            overflow-y: auto;
            background: #1e1e1e;
            color: #f8f8f2;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 13px;
            line-height: 1.5;
        }

        .log-line {
            margin-bottom: 2px;
            padding: 4px 8px;
            border-radius: 3px;
            white-space: pre-wrap;
            word-break: break-all;
        }

        .log-line:hover {
            background: rgba(255, 255, 255, 0.05);
        }

        .log-timestamp {
            color: #8be9fd;
            margin-right: 8px;
        }

        .log-level {
            margin-right: 8px;
            font-weight: 600;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 11px;
        }

        .log-info .log-level {
            background: #50fa7b;
            color: #282a36;
        }

        .log-warn .log-level {
            background: #ffb86c;
            color: #282a36;
        }

        .log-error .log-level {
            background: #ff5555;
            color: white;
        }

        .log-debug .log-level {
            background: #bd93f9;
            color: white;
        }

        .log-message {
            color: #f8f8f2;
        }

        .empty-state {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
            color: #94a3b8;
        }

        .empty-state .icon {
            font-size: 48px;
            margin-bottom: 16px;
            opacity: 0.5;
        }

        .empty-state h3 {
            font-size: 18px;
            margin-bottom: 8px;
            color: #cbd5e1;
        }

        .empty-state p {
            font-size: 14px;
            text-align: center;
            max-width: 300px;
        }

        .loading {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
            flex-direction: column;
            color: #94a3b8;
        }

        .loading .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #334155;
            border-top: 4px solid #60a5fa;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 16px;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }

        .error-state {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
            color: #f87171;
            text-align: center;
            padding: 20px;
        }

        .error-state .icon {
            font-size: 48px;
            margin-bottom: 16px;
        }

        .auto-refresh {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .switch {
            position: relative;
            display: inline-block;
            width: 44px;
            height: 24px;
        }

        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #475569;
            transition: .4s;
            border-radius: 24px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked+.slider {
            background-color: #60a5fa;
        }

        input:checked+.slider:before {
            transform: translateX(20px);
        }

        /* 滚动条样式 */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: rgba(30, 41, 59, 0.5);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb {
            background: rgba(96, 165, 250, 0.6);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: rgba(96, 165, 250, 0.8);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .dashboard {
                flex-direction: column;
            }

            .sidebar {
                width: 100%;
                height: 200px;
            }

            .instance-list {
                display: flex;
                overflow-x: auto;
                overflow-y: hidden;
                padding: 10px;
            }

            .instance-item {
                min-width: 200px;
                margin-right: 8px;
                margin-bottom: 0;
            }
        }
    </style>
</head>

<body>
    <div class="dashboard">
        <!-- 左侧栏 - 策略实例列表 -->
        <div class="sidebar">
            <div class="sidebar-header">
                <h1>🎯 策略实例</h1>
                <div class="subtitle">Strategy Instances</div>
            </div>

            <div class="sidebar-stats">
                <div class="stats-item">
                    <span class="stats-label">总实例数</span>
                    <span class="stats-value" id="totalInstances">-</span>
                </div>
                <div class="stats-item">
                    <span class="stats-label">活跃实例</span>
                    <span class="stats-value" id="activeInstances">-</span>
                </div>
                <div class="stats-item">
                    <span class="stats-label">最后更新</span>
                    <span class="stats-value" id="lastUpdate">-</span>
                </div>
            </div>

            <div class="instance-list" id="instanceList">
                <div class="loading">
                    <div class="spinner"></div>
                    <p>正在加载策略实例...</p>
                </div>
            </div>
        </div>

        <!-- 右侧主内容区 -->
        <div class="main-content">
            <div class="main-header">
                <div class="main-title" id="mainTitle">策略日志仪表盘</div>
                <div class="main-subtitle" id="mainSubtitle">请从左侧选择一个策略实例查看日志</div>
            </div>

            <div class="log-controls">
                <div class="control-group">
                    <label class="control-label">显示行数:</label>
                    <select class="control-input" id="linesSelect">
                        <option value="50">50行</option>
                        <option value="100">100行</option>
                        <option value="200">200行</option>
                        <option value="500" selected>500行</option>
                        <option value="1000">1000行</option>
                    </select>
                </div>

                <div class="control-group auto-refresh">
                    <label class="control-label">自动刷新:</label>
                    <label class="switch">
                        <input type="checkbox" id="autoRefresh">
                        <span class="slider"></span>
                    </label>
                </div>

                <button class="btn btn-primary" id="refreshBtn">
                    🔄 刷新日志
                </button>

                <button class="btn btn-secondary" id="clearBtn">
                    🗑️ 清空显示
                </button>

                <div class="control-group">
                    <label class="control-label">筛选:</label>
                    <input type="text" class="control-input" id="filterInput" placeholder="输入关键词筛选日志..."
                        style="width: 200px;">
                </div>

                <button class="btn btn-primary" id="filterBtn">
                    🔍 筛选
                </button>

                <button class="btn btn-secondary" id="clearFilterBtn" style="display: none;">
                    🔄 恢复默认
                </button>

                <button class="btn btn-secondary" id="showAllBtn" style="display: none;">
                    📄 显示全部
                </button>
            </div>

            <div class="log-container" id="logContainer">
                <div class="empty-state">
                    <div class="icon">📋</div>
                    <h3>选择策略实例</h3>
                    <p>请从左侧栏选择一个策略实例来查看其日志记录</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        class LogsDashboard {
            constructor() {
                this.apiBaseUrl = '/api';
                this.instances = [];
                this.selectedInstance = null;
                this.autoRefreshInterval = null;
                this.isLoading = false;
                this.currentLogs = []; // 存储当前加载的所有日志
                this.filteredLogs = []; // 存储筛选后的日志
                this.currentFilter = ''; // 当前筛选关键词
                this.isFiltered = false; // 初始不启用筛选状态

                this.initializeElements();
                this.bindEvents();
                this.loadInstances();
            }

            initializeElements() {
                this.elements = {
                    instanceList: document.getElementById('instanceList'),
                    logContainer: document.getElementById('logContainer'),
                    mainTitle: document.getElementById('mainTitle'),
                    mainSubtitle: document.getElementById('mainSubtitle'),
                    totalInstances: document.getElementById('totalInstances'),
                    activeInstances: document.getElementById('activeInstances'),
                    lastUpdate: document.getElementById('lastUpdate'),
                    linesSelect: document.getElementById('linesSelect'),
                    autoRefresh: document.getElementById('autoRefresh'),
                    refreshBtn: document.getElementById('refreshBtn'),
                    clearBtn: document.getElementById('clearBtn'),
                    filterInput: document.getElementById('filterInput'),
                    filterBtn: document.getElementById('filterBtn'),
                    clearFilterBtn: document.getElementById('clearFilterBtn'),
                    showAllBtn: document.getElementById('showAllBtn')
                };
            }

            bindEvents() {
                this.elements.refreshBtn.addEventListener('click', () => {
                    if (this.selectedInstance) {
                        this.loadInstanceLogs(this.selectedInstance);
                    } else {
                        this.loadInstances();
                    }
                });

                this.elements.clearBtn.addEventListener('click', () => {
                    this.clearLogDisplay();
                });

                this.elements.autoRefresh.addEventListener('change', (e) => {
                    if (e.target.checked) {
                        this.startAutoRefresh();
                    } else {
                        this.stopAutoRefresh();
                    }
                });

                this.elements.linesSelect.addEventListener('change', () => {
                    if (this.selectedInstance) {
                        this.loadInstanceLogs(this.selectedInstance);
                    }
                });

                this.elements.filterBtn.addEventListener('click', () => {
                    this.applyFilter();
                });

                this.elements.clearFilterBtn.addEventListener('click', () => {
                    this.clearFilter();
                });

                this.elements.filterInput.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        this.applyFilter();
                    }
                });

                this.elements.showAllBtn.addEventListener('click', () => {
                    this.showAllLogs();
                });
            }

            async loadInstances() {
                try {
                    this.showInstancesLoading();

                    const response = await fetch(`${this.apiBaseUrl}/logs/instances`);
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }

                    const data = await response.json();
                    if (!data.success) {
                        throw new Error(data.error || '获取实例列表失败');
                    }

                    this.instances = data.instances;
                    this.renderInstances();
                    this.updateStats();

                } catch (error) {
                    console.error('加载实例列表失败:', error);
                    this.showInstancesError(error.message);
                }
            }

            showInstancesLoading() {
                this.elements.instanceList.innerHTML = `
                    <div class="loading">
                        <div class="spinner"></div>
                        <p>正在加载策略实例...</p>
                    </div>
                `;
            }

            showInstancesError(message) {
                this.elements.instanceList.innerHTML = `
                    <div class="error-state">
                        <div class="icon">❌</div>
                        <h3>加载失败</h3>
                        <p>${message}</p>
                    </div>
                `;
            }

            renderInstances() {
                if (this.instances.length === 0) {
                    this.elements.instanceList.innerHTML = `
                        <div class="empty-state">
                            <div class="icon">📂</div>
                            <h3>未找到策略实例</h3>
                            <p>请确保策略正在运行并生成日志文件</p>
                        </div>
                    `;
                    return;
                }

                const instancesHtml = this.instances.map(instance => {
                    const isActive = this.selectedInstance === instance.instanceId;
                    let displayName, icon, statusText, statusClass;

                    if (instance.instanceId === 'api-server') {
                        // API服务器日志
                        displayName = '🖥️ API服务器';
                        icon = '🖥️';
                        statusText = '系统';
                        statusClass = 'system-log';
                    } else {
                        // 策略实例日志
                        const shortId = this.formatInstanceId(instance.instanceId);
                        
                        // 🔧 根据策略类型设置显示名称和图标
                        if (instance.strategyType === 'simple-y' || instance.instanceId.includes('simple-y_')) {
                            const strategyName = instance.strategyName || '未命名';
                            displayName = `🎯 简单Y策略 ${strategyName} ${shortId}`;
                            icon = '🎯';
                            statusText = '活跃';
                            statusClass = 'strategy-log simple-y-log';
                        } else {
                            const strategyName = instance.strategyName || '未命名';
                            displayName = `🔗 连锁头寸 ${strategyName} ${shortId}`;
                            icon = '🔗';
                            statusText = '活跃';
                            statusClass = 'strategy-log chain-position-log';
                        }
                    }

                    return `
                        <div class="instance-item ${isActive ? 'active' : ''} ${statusClass}" 
                             data-instance-id="${instance.instanceId}">
                            <div class="instance-header">
                                <div class="instance-id">${displayName}</div>
                                <div class="instance-meta">
                                    <div class="instance-status">
                                        <div class="status-dot"></div>
                                        <span>${statusText}</span>
                                    </div>
                                    <span>${this.formatFileSize(instance.size)}</span>
                                </div>
                            </div>
                        </div>
                    `;
                }).join('');

                this.elements.instanceList.innerHTML = instancesHtml;

                // 绑定点击事件
                this.elements.instanceList.querySelectorAll('.instance-item').forEach(item => {
                    item.addEventListener('click', () => {
                        const instanceId = item.dataset.instanceId;
                        this.selectInstance(instanceId);
                    });
                });
            }

            formatInstanceId(instanceId) {
                // 只显示末尾的简化ID，通常是下划线后的短ID
                const parts = instanceId.split('_');
                if (parts.length >= 3) {
                    // 返回最后一部分作为简化ID (例如: tk93bn)
                    return parts[parts.length - 1];
                }
                // 如果格式不符合预期，返回最后8个字符
                return instanceId.length > 8 ? instanceId.substring(instanceId.length - 8) : instanceId;
            }

            formatFileSize(bytes) {
                if (bytes === 0) return '0 B';
                const k = 1024;
                const sizes = ['B', 'KB', 'MB', 'GB'];
                const i = Math.floor(Math.log(bytes) / Math.log(k));
                return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
            }

            selectInstance(instanceId) {
                // 更新选中状态
                this.selectedInstance = instanceId;

                // 更新UI选中状态
                this.elements.instanceList.querySelectorAll('.instance-item').forEach(item => {
                    item.classList.remove('active');
                });

                const selectedItem = this.elements.instanceList.querySelector(`[data-instance-id="${instanceId}"]`);
                if (selectedItem) {
                    selectedItem.classList.add('active');
                }

                // 更新标题
                const instance = this.instances.find(inst => inst.instanceId === instanceId);
                let titleText, subtitleText;

                if (instanceId === 'api-server') {
                    // API服务器日志
                    titleText = `🖥️ API服务器日志`;
                    subtitleText = `系统运行日志 - 实时监控`;
                } else {
                    // 策略实例日志
                    const shortId = this.formatInstanceId(instanceId);
                    
                    // 🔧 根据策略类型设置显示标题
                    if (instance?.strategyType === 'simple-y' || instanceId.includes('simple-y_')) {
                        const strategyName = instance?.strategyName || '未命名';
                        titleText = `🎯 简单Y策略 ${strategyName} ${shortId}`;
                        subtitleText = `简单Y头寸策略实例日志 - 实时监控`;
                    } else {
                        const strategyName = instance?.strategyName || '未命名';
                        titleText = `🔗 连锁头寸 ${strategyName} ${shortId}`;
                        subtitleText = `连锁头寸策略实例日志 - 实时监控`;
                    }
                }

                this.elements.mainTitle.textContent = titleText;
                this.elements.mainSubtitle.textContent = subtitleText;

                // 加载日志
                this.loadInstanceLogs(instanceId);
            }

            async loadInstanceLogs(instanceId) {
                if (this.isLoading) return;

                try {
                    this.isLoading = true;
                    this.showLogLoading();

                    const lines = this.elements.linesSelect.value;
                    const response = await fetch(`${this.apiBaseUrl}/logs/${instanceId}?lines=${lines}`);

                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }

                    const data = await response.json();
                    if (!data.success) {
                        throw new Error(data.error || '获取日志失败');
                    }

                    this.renderLogs(data.logs);
                    this.updateLogStats(data);

                } catch (error) {
                    console.error('加载日志失败:', error);
                    this.showLogError(error.message);
                } finally {
                    this.isLoading = false;
                }
            }

            showLogLoading() {
                this.elements.logContainer.innerHTML = `
                    <div class="loading">
                        <div class="spinner"></div>
                        <p>正在加载日志...</p>
                    </div>
                `;
            }

            showLogError(message) {
                this.elements.logContainer.innerHTML = `
                    <div class="error-state">
                        <div class="icon">❌</div>
                        <h3>加载日志失败</h3>
                        <p>${message}</p>
                    </div>
                `;
            }

            renderLogs(logs) {
                // 存储当前加载的日志
                this.currentLogs = logs;

                // 如果正在筛选状态，应用筛选
                if (this.isFiltered) {
                    this.filteredLogs = this.filterLogs(logs, this.currentFilter);
                    this.displayLogs(this.filteredLogs);
                } else {
                    this.displayLogs(logs);
                }
            }

            displayLogs(logs) {
                if (logs.length === 0) {
                    const emptyMessage = this.isFiltered ?
                        `<div class="empty-state">
                            <div class="icon">🔍</div>
                            <h3>未找到匹配的日志</h3>
                            <p>没有找到包含 "${this.currentFilter}" 的日志记录</p>
                        </div>` :
                        `<div class="empty-state">
                            <div class="icon">📄</div>
                            <h3>暂无日志数据</h3>
                            <p>该策略实例尚未生成日志记录</p>
                        </div>`;

                    this.elements.logContainer.innerHTML = emptyMessage;
                    return;
                }

                const logsHtml = logs.map(line => {
                    const parsed = this.parseLogLine(line);
                    const levelClass = this.getLogLevelClass(parsed.level);

                    return `
                        <div class="log-line ${levelClass}">
                            <span class="log-timestamp">${parsed.timestamp}</span>
                            <span class="log-level">[${parsed.level}]</span>
                            <span class="log-message">${this.escapeHtml(parsed.message)}</span>
                        </div>
                    `;
                }).join('');

                this.elements.logContainer.innerHTML = logsHtml;

                // 滚动到底部
                this.elements.logContainer.scrollTop = this.elements.logContainer.scrollHeight;
            }

            parseLogLine(line) {
                // 解析日志格式: 06/24 09:06:49 INFO [strategy-xxx] OP: message
                const match = line.match(/^(\d{2}\/\d{2} \d{2}:\d{2}:\d{2})\s+(\w+)\s+\[([^\]]+)\]\s+(.+)$/);

                if (match) {
                    return {
                        timestamp: match[1],
                        level: match[2],
                        component: match[3],
                        message: match[4]
                    };
                }

                return {
                    timestamp: '',
                    level: 'INFO',
                    component: '',
                    message: line
                };
            }

            getLogLevelClass(level) {
                switch (level?.toUpperCase()) {
                    case 'ERROR': return 'log-error';
                    case 'WARN': case 'WARNING': return 'log-warn';
                    case 'DEBUG': return 'log-debug';
                    case 'INFO': default: return 'log-info';
                }
            }

            escapeHtml(text) {
                const div = document.createElement('div');
                div.textContent = text;
                return div.innerHTML;
            }

            updateStats() {
                // 计算策略实例数量（排除API服务器）
                const strategyInstances = this.instances.filter(inst => inst.instanceId !== 'api-server');

                this.elements.totalInstances.textContent = strategyInstances.length;
                this.elements.activeInstances.textContent = strategyInstances.length;
                this.elements.lastUpdate.textContent = new Date().toLocaleTimeString();
            }

            updateLogStats(data) {
                const instance = this.instances.find(inst => inst.instanceId === data.instanceId);
                let displayName;

                if (data.instanceId === 'api-server') {
                    // API服务器日志
                    displayName = 'API服务器';
                } else {
                    // 策略实例日志
                    const shortId = this.formatInstanceId(data.instanceId);
                    
                    // 🔧 根据策略类型设置显示名称
                    if (instance?.strategyType === 'simple-y' || data.instanceId.includes('simple-y_')) {
                        const strategyName = instance?.strategyName || '未命名';
                        displayName = `简单Y策略 ${strategyName} ${shortId}`;
                    } else {
                        const strategyName = instance?.strategyName || '未命名';
                        displayName = `连锁头寸 ${strategyName} ${shortId}`;
                    }
                }

                this.elements.mainSubtitle.textContent =
                    `${displayName} • 显示最新 ${data.logs.length}/${data.totalLines} 行 • 文件大小 ${this.formatFileSize(data.fileSize)}`;
            }

            clearLogDisplay() {
                // 清空日志数据
                this.currentLogs = [];
                this.filteredLogs = [];

                // 保持筛选状态但清空显示
                this.elements.logContainer.innerHTML = `
                    <div class="empty-state">
                        <div class="icon">🧹</div>
                        <h3>显示已清空</h3>
                        <p>点击刷新按钮重新加载日志</p>
                    </div>
                `;
            }

            startAutoRefresh() {
                if (this.autoRefreshInterval) {
                    clearInterval(this.autoRefreshInterval);
                }

                this.autoRefreshInterval = setInterval(() => {
                    if (this.selectedInstance && !this.isLoading) {
                        this.loadInstanceLogs(this.selectedInstance);
                    }
                }, 5000); // 每5秒刷新一次
            }

            stopAutoRefresh() {
                if (this.autoRefreshInterval) {
                    clearInterval(this.autoRefreshInterval);
                    this.autoRefreshInterval = null;
                }
            }

            applyFilter() {
                let filterText = this.elements.filterInput.value.trim();

                // 如果输入为空，根据日志类型使用默认筛选条件
                if (!filterText) {
                    if (this.selectedInstance === 'api-server') {
                        filterText = 'INFO'; // API服务器日志默认筛选INFO级别
                    } else if (this.selectedInstance && this.selectedInstance.includes('simple-y_')) {
                        // 🔧 简单Y策略的默认筛选条件
                        filterText = '简单Y'; // 筛选包含"简单Y"的日志
                    } else {
                        // 连锁头寸策略默认筛选条件
                        filterText = '头寸重建触发';
                    }
                    this.elements.filterInput.value = filterText;
                }

                if (this.currentLogs.length === 0) {
                    this.showFilterError('请先加载日志数据');
                    return;
                }

                this.currentFilter = filterText;
                this.isFiltered = true;

                // 筛选日志
                this.filteredLogs = this.filterLogs(this.currentLogs, filterText);

                // 显示筛选结果
                this.displayLogs(this.filteredLogs);

                // 更新UI状态
                this.updateFilterUI();

                // 更新统计信息
                this.updateFilterStats();
            }

            clearFilter() {
                // 根据日志类型恢复到默认筛选状态
                let defaultFilter;
                if (this.selectedInstance === 'api-server') {
                    defaultFilter = 'INFO'; // API服务器日志默认筛选INFO级别
                } else if (this.selectedInstance && this.selectedInstance.includes('simple-y_')) {
                    // 🔧 简单Y策略的默认筛选条件
                    defaultFilter = '简单Y'; // 筛选包含"简单Y"的日志
                } else {
                    // 连锁头寸策略默认筛选条件
                    defaultFilter = '头寸重建触发';
                }

                this.currentFilter = defaultFilter;
                this.isFiltered = true;
                this.elements.filterInput.value = defaultFilter;

                // 应用默认筛选
                if (this.currentLogs.length > 0) {
                    this.filteredLogs = this.filterLogs(this.currentLogs, this.currentFilter);
                    this.displayLogs(this.filteredLogs);
                    this.updateFilterStats();
                } else {
                    this.displayLogs([]);
                }

                // 更新UI状态
                this.updateFilterUI();
            }

            filterLogs(logs, filterText) {
                if (!filterText) return logs;

                const lowerFilter = filterText.toLowerCase();
                return logs.filter(line =>
                    line.toLowerCase().includes(lowerFilter)
                );
            }

            updateFilterUI() {
                if (this.isFiltered) {
                    this.elements.filterBtn.style.display = 'none';
                    this.elements.clearFilterBtn.style.display = 'inline-block';
                    this.elements.showAllBtn.style.display = 'inline-block';
                    this.elements.clearFilterBtn.className = 'btn btn-danger';
                    this.elements.filterInput.style.backgroundColor = 'rgba(16, 185, 129, 0.2)';
                    this.elements.filterInput.style.borderColor = '#10b981';
                } else {
                    this.elements.filterBtn.style.display = 'inline-block';
                    this.elements.clearFilterBtn.style.display = 'none';
                    this.elements.showAllBtn.style.display = 'none';
                    this.elements.filterInput.style.backgroundColor = 'rgba(30, 41, 59, 0.8)';
                    this.elements.filterInput.style.borderColor = '#475569';
                }
            }

            updateFilterStats() {
                if (this.isFiltered) {
                    const totalLines = this.currentLogs.length;
                    const filteredLines = this.filteredLogs.length;

                    // 获取当前实例的显示名称
                    let instanceName = '日志';
                    if (this.selectedInstance) {
                        const instance = this.instances.find(inst => inst.instanceId === this.selectedInstance);
                        if (this.selectedInstance === 'api-server') {
                            instanceName = 'API服务器';
                        } else {
                            const shortId = this.formatInstanceId(this.selectedInstance);
                            
                            // 🔧 根据策略类型设置显示名称
                            if (instance?.strategyType === 'simple-y' || this.selectedInstance.includes('simple-y_')) {
                                const strategyName = instance?.strategyName || '未命名';
                                instanceName = `简单Y策略 ${strategyName} ${shortId}`;
                            } else {
                                const strategyName = instance?.strategyName || '未命名';
                                instanceName = `连锁头寸 ${strategyName} ${shortId}`;
                            }
                        }
                    }

                    this.elements.mainSubtitle.textContent =
                        `${instanceName} • 筛选结果: ${filteredLines}/${totalLines} 条日志包含 "${this.currentFilter}"`;
                }
            }

            showAllLogs() {
                // 关闭筛选，显示所有日志
                this.currentFilter = '';
                this.isFiltered = false;
                this.filteredLogs = [];
                this.elements.filterInput.value = '';

                // 显示所有日志
                this.displayLogs(this.currentLogs);

                // 更新UI状态
                this.updateFilterUI();

                // 恢复统计信息
                this.updateLogStats({
                    logs: this.currentLogs,
                    totalLines: this.currentLogs.length,
                    instanceId: this.selectedInstance
                });
            }

            showFilterError(message) {
                // 临时显示错误提示
                const originalPlaceholder = this.elements.filterInput.placeholder;
                this.elements.filterInput.placeholder = message;
                this.elements.filterInput.style.borderColor = '#ef4444';

                setTimeout(() => {
                    this.elements.filterInput.placeholder = originalPlaceholder;
                    this.elements.filterInput.style.borderColor = '#475569';
                }, 2000);
            }
        }

        // 初始化仪表盘
        document.addEventListener('DOMContentLoaded', () => {
            new LogsDashboard();
        });
    </script>
</body>

</html>