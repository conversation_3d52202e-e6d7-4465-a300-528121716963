# SSL证书配置 (HTTPS支持)
# 如果您有SSL证书，请设置以下路径
# SSL_KEY_PATH=/path/to/your/ssl/private-key.pem
# SSL_CERT_PATH=/path/to/your/ssl/certificate.pem

# 🔒 SSL证书示例配置
# SSL_KEY_PATH=/etc/ssl/private/server.key
# SSL_CERT_PATH=/etc/ssl/certs/server.crt
# 或者使用Let's Encrypt证书
# SSL_KEY_PATH=/etc/letsencrypt/live/yourdomain.com/privkey.pem
# SSL_CERT_PATH=/etc/letsencrypt/live/yourdomain.com/fullchain.pem

# 🚀 DLMM增强版应用程序环境配置
# 复制此文件为 .env 并根据需要修改配置

# ===== 基础设置 =====
API_PORT=7000
WS_PORT=7002
SOLANA_NETWORK=mainnet-beta

# ===== 功能开关 =====
# 逐步启用功能，避免一次性加载过多服务

# 基础设施服务（始终启用）
ENABLE_LOGGING=true

# 内部服务（推荐启用）
ENABLE_CONFIG_SERVICE=true
ENABLE_STATE_SERVICE=true  
ENABLE_CACHE_SERVICE=true

# 区块链服务（按需启用）
ENABLE_BLOCKCHAIN_SERVICES=false  # 设为 true 启用 Solana、钱包等服务
SOLANA_RPC_URL=https://api.mainnet-beta.solana.com
WALLET_PRIVATE_KEY_PATH=./config/wallet.json

# 外部服务（按需启用）
ENABLE_EXTERNAL_SERVICES=false   # 设为 true 启用 Jupiter、Meteora 等外部API
JUPITER_API_URL=https://quote-api.jup.ag/v6
METEORA_API_URL=https://dlmm-api.meteora.ag

# 业务服务（按需启用）
ENABLE_BUSINESS_SERVICES=false   # 设为 true 启用头寸管理等业务功能

# 监控服务（可选）
ENABLE_MONITORING=false          # 设为 true 启用健康检查监控

# ===== 渐进式启用策略 =====
# 
# 阶段1: 基础启动
# - 只启用日志和API服务器
# - 所有功能开关都设为 false
# 
# 阶段2: 核心服务
# - ENABLE_CONFIG_SERVICE=true
# - ENABLE_STATE_SERVICE=true
# - ENABLE_CACHE_SERVICE=true
# 
# 阶段3: 区块链集成
# - ENABLE_BLOCKCHAIN_SERVICES=true
# - 配置正确的钱包和RPC
# 
# 阶段4: 外部API集成
# - ENABLE_EXTERNAL_SERVICES=true
# - 测试Jupiter和Meteora连接
# 
# 阶段5: 完整业务功能
# - ENABLE_BUSINESS_SERVICES=true
# - ENABLE_MONITORING=true

# ===== 日志配置 =====
LOG_LEVEL=INFO
LOG_FILE_MAX_SIZE=10MB
LOG_MAX_FILES=5

# ===== 性能配置 =====
SERVICE_INIT_TIMEOUT=30000
HEALTH_CHECK_INTERVAL=30000
CACHE_TTL=300000 