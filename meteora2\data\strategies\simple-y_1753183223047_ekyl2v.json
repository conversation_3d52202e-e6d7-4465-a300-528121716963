{"id": "simple-y_1753183223047_ekyl2v", "type": "simple-y", "name": "TiA", "config": {"poolAddress": "4iaADKvCHtcsXGmaoZHNuiaco1rZcyMAuey8RsPUsYbJ", "positionAmount": 1, "binRange": 69, "monitoringInterval": 30, "outOfRangeTimeout": 600, "yieldExtractionThreshold": 0.01, "yieldExtractionTimeLock": 2, "maxPriceForRecreation": 1e-06, "minPriceForRecreation": 3.5e-06, "slippageBps": 1000, "benchmarkYieldThreshold5Min": 2, "minActiveBinPositionThreshold": 10, "enableSmartStopLoss": true, "stopLoss": {"activeBinSafetyThreshold": -10, "observationPeriodMinutes": 15, "lossThresholdPercentage": 4}, "positionRecreation": {"enableMarketOpportunityRecreation": true, "marketOpportunity": {"positionThreshold": 90, "profitThreshold": 3}, "enableLossRecoveryRecreation": true, "lossRecovery": {"markPositionThreshold": 30, "markLossThreshold": 2, "triggerPositionThreshold": 90, "triggerProfitThreshold": 2}, "enableDynamicProfitRecreation": true, "dynamicProfitRecreation": {"positionThreshold": 90, "benchmarkTier1Max": 1, "benchmarkTier2Max": 3, "benchmarkTier3Max": 5, "benchmarkTier4Max": 50, "profitThresholdTier1": 1, "profitThresholdTier2": 3, "profitThresholdTier3": 5, "profitThresholdTier4": 8}}}, "status": "stopped", "createdAt": "2025-07-22T11:20:23.047Z", "startedAt": "2025-07-22T12:47:15.000Z", "stoppedAt": "2025-07-22T13:52:59.609Z"}