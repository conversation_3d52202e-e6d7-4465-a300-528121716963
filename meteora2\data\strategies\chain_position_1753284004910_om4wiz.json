{"id": "chain_position_1753284004910_om4wiz", "type": "chain_position", "name": "dumbmoney", "config": {"poolAddress": "5qP3PM7BPUDvcA7z6Ha9Vysgm8TrW4hJcgZBjMJutsLF", "positionAmount": 8, "monitoringInterval": 12, "outOfRangeTimeout": 600, "yieldExtractionThreshold": 0.02, "yieldExtractionTimeLock": 1, "maxPriceForRecreation": 1e-06, "minPriceForRecreation": 3.4e-06, "benchmarkYieldThreshold5Min": 2, "minActiveBinPositionThreshold": 35, "chainPositionType": "DUAL_CHAIN", "enableSmartStopLoss": true, "stopLoss": {"activeBinSafetyThreshold": 30, "observationPeriodMinutes": 5, "lossThresholdPercentage": 2}, "positionRecreation": {"enableMarketOpportunityRecreation": true, "enableLossRecoveryRecreation": true, "enableDynamicProfitRecreation": true, "marketOpportunity": {"positionThreshold": 90, "profitThreshold": 1.5}, "lossRecovery": {"markPositionThreshold": 40, "markLossThreshold": 0.5, "triggerPositionThreshold": 90, "triggerProfitThreshold": 1.2}, "dynamicProfitRecreation": {"positionThreshold": 90, "benchmarkTier1Max": 0.5, "benchmarkTier2Max": 1.5, "benchmarkTier3Max": 2.5, "benchmarkTier4Max": 50, "profitThresholdTier1": 0.5, "profitThresholdTier2": 1.5, "profitThresholdTier3": 2.5, "profitThresholdTier4": 4}}, "stopLossConfig": {"maxLossPercentage": 0.05, "checkInterval": 30, "conditions": ["PRICE_DEVIATION", "TIME_BASED"]}}, "status": "running", "createdAt": "2025-07-23T15:20:04.911Z", "startedAt": "2025-07-23T15:55:04.885Z", "stoppedAt": "2025-07-23T15:50:17.805Z"}