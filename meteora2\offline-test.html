<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧪 DLMM离线测试页面</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .content {
            padding: 40px;
        }

        .section {
            margin-bottom: 40px;
            padding: 30px;
            border: 2px solid #e1e8ed;
            border-radius: 10px;
            background: #f8f9fa;
        }

        .section h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.5em;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1.1em;
            font-weight: 600;
            transition: all 0.3s ease;
            margin: 10px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .btn.success {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
        }

        .btn.warning {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #2c3e50;
        }

        .form-group input, .form-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e8ed;
            border-radius: 8px;
            font-size: 1em;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .result-box {
            margin-top: 15px;
            padding: 15px;
            border-radius: 8px;
            background: #f8f9fa;
            border-left: 4px solid #17a2b8;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            max-height: 300px;
            overflow-y: auto;
        }

        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .status-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-left: 4px solid #3498db;
        }

        .status-value {
            font-size: 1.8em;
            font-weight: bold;
            color: #3498db;
        }

        .status-label {
            color: #7f8c8d;
            margin-top: 5px;
        }

        .info-box {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 15px;
            margin: 15px 0;
            border-radius: 4px;
        }

        .success-box {
            background: #e8f5e8;
            border-left: 4px solid #4caf50;
            padding: 15px;
            margin: 15px 0;
            border-radius: 4px;
        }

        .warning-box {
            background: #fff3e0;
            border-left: 4px solid #ff9800;
            padding: 15px;
            margin: 15px 0;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 DLMM离线测试页面</h1>
            <p>完全独立运行，无需服务器的测试环境</p>
        </div>

        <div class="content">
            <!-- 系统状态 -->
            <div class="section">
                <h2>📊 系统状态监控</h2>
                <div class="status-grid">
                    <div class="status-card">
                        <div class="status-value" id="uptime">0s</div>
                        <div class="status-label">运行时间</div>
                    </div>
                    <div class="status-card">
                        <div class="status-value" id="testCount">0</div>
                        <div class="status-label">测试次数</div>
                    </div>
                    <div class="status-card">
                        <div class="status-value" id="successRate">100%</div>
                        <div class="status-label">成功率</div>
                    </div>
                    <div class="status-card">
                        <div class="status-value" id="networkStatus">Devnet</div>
                        <div class="status-label">网络</div>
                    </div>
                </div>
            </div>

            <!-- 钱包测试 -->
            <div class="section">
                <h2>🔑 测试钱包管理</h2>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="walletAddress">钱包地址</label>
                        <input type="text" id="walletAddress" placeholder="点击生成测试钱包" readonly>
                    </div>
                    <div class="form-group">
                        <label for="walletBalance">SOL余额</label>
                        <input type="text" id="walletBalance" value="2.5 SOL" readonly>
                    </div>
                </div>

                <button class="btn" onclick="generateTestWallet()">🔑 生成测试钱包</button>
                <button class="btn success" onclick="airdropSOL()">💰 空投测试SOL</button>
                <button class="btn warning" onclick="checkBalance()">💳 检查余额</button>

                <div id="walletResult" class="result-box" style="display:none;"></div>
            </div>

            <!-- 策略创建测试 -->
            <div class="section">
                <h2>🚀 策略创建测试</h2>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="strategyName">策略名称</label>
                        <input type="text" id="strategyName" value="测试策略_001">
                    </div>
                    <div class="form-group">
                        <label for="strategyType">策略类型</label>
                        <select id="strategyType">
                            <option value="simple_y">SimpleY策略</option>
                            <option value="grid">网格策略</option>
                        </select>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="yAmount">Y代币数量</label>
                        <input type="number" id="yAmount" value="10" min="1">
                    </div>
                    <div class="form-group">
                        <label for="binRange">价格区间</label>
                        <input type="number" id="binRange" value="30" min="5">
                    </div>
                </div>

                <button class="btn" onclick="createTestStrategy()">🎯 创建测试策略</button>
                <button class="btn success" onclick="simulateStrategy()">🧪 模拟运行</button>
                <button class="btn warning" onclick="stopStrategy()">⏹️ 停止策略</button>

                <div id="strategyResult" class="result-box" style="display:none;"></div>
            </div>

            <!-- 交易模拟 -->
            <div class="section">
                <h2>💱 交易模拟测试</h2>
                
                <div class="info-box">
                    💡 <strong>模拟交易：</strong> 所有交易都是模拟的，不会消耗真实代币
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="fromToken">源代币</label>
                        <select id="fromToken">
                            <option value="SOL">SOL</option>
                            <option value="USDC">USDC</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="toToken">目标代币</label>
                        <select id="toToken">
                            <option value="USDC">USDC</option>
                            <option value="SOL">SOL</option>
                        </select>
                    </div>
                </div>

                <div class="form-group">
                    <label for="swapAmount">交换数量</label>
                    <input type="number" id="swapAmount" value="1" min="0.1" step="0.1">
                </div>

                <button class="btn" onclick="simulateSwap()">🔄 模拟交换</button>
                <button class="btn success" onclick="getQuote()">💰 获取报价</button>

                <div id="swapResult" class="result-box" style="display:none;"></div>
            </div>

            <!-- 测试结果 -->
            <div class="section">
                <h2>📈 测试结果总结</h2>
                
                <div class="success-box">
                    🎉 <strong>测试环境就绪！</strong> 您可以安全地测试所有功能
                </div>

                <div id="testSummary">
                    <h3>测试完成情况：</h3>
                    <ul id="testChecklist">
                        <li>☐ 钱包生成测试</li>
                        <li>☐ 余额检查测试</li>
                        <li>☐ 策略创建测试</li>
                        <li>☐ 交易模拟测试</li>
                    </ul>
                </div>

                <button class="btn success" onclick="runAllTests()">🧪 运行全部测试</button>
                <button class="btn" onclick="exportResults()">📊 导出结果</button>
            </div>
        </div>
    </div>

    <script>
        let startTime = Date.now();
        let testCount = 0;
        let successCount = 0;
        let testWallet = null;
        let testStrategies = [];

        // 更新状态显示
        function updateStatus() {
            const uptime = Date.now() - startTime;
            document.getElementById('uptime').textContent = formatTime(uptime);
            document.getElementById('testCount').textContent = testCount;
            document.getElementById('successRate').textContent = 
                testCount > 0 ? Math.round((successCount / testCount) * 100) + '%' : '100%';
        }

        function formatTime(ms) {
            const seconds = Math.floor(ms / 1000);
            const minutes = Math.floor(seconds / 60);
            if (minutes > 0) {
                return `${minutes}m ${seconds % 60}s`;
            }
            return `${seconds}s`;
        }

        // 生成测试钱包
        function generateTestWallet() {
            testCount++;
            const address = 'Test' + Math.random().toString(36).substr(2, 9);
            testWallet = {
                address: address,
                balance: { SOL: 2.5, USDC: 100 },
                network: 'devnet'
            };
            
            document.getElementById('walletAddress').value = address;
            showResult('walletResult', {
                success: true,
                wallet: testWallet,
                message: '测试钱包生成成功'
            }, '✅ 钱包生成成功');
            
            successCount++;
            updateTestChecklist(0);
        }

        // 空投SOL
        function airdropSOL() {
            testCount++;
            if (!testWallet) {
                alert('请先生成测试钱包');
                return;
            }
            
            testWallet.balance.SOL += 2;
            document.getElementById('walletBalance').value = testWallet.balance.SOL + ' SOL';
            
            showResult('walletResult', {
                success: true,
                amount: 2,
                newBalance: testWallet.balance.SOL,
                txHash: 'airdrop_' + Date.now()
            }, '💰 空投成功');
            
            successCount++;
        }

        // 检查余额
        function checkBalance() {
            testCount++;
            if (!testWallet) {
                alert('请先生成测试钱包');
                return;
            }
            
            showResult('walletResult', {
                success: true,
                balances: testWallet.balance,
                address: testWallet.address
            }, '💳 余额检查完成');
            
            successCount++;
            updateTestChecklist(1);
        }

        // 创建测试策略
        function createTestStrategy() {
            testCount++;
            const strategy = {
                id: 'strategy_' + Date.now(),
                name: document.getElementById('strategyName').value,
                type: document.getElementById('strategyType').value,
                yAmount: parseFloat(document.getElementById('yAmount').value),
                binRange: parseInt(document.getElementById('binRange').value),
                status: 'created',
                testMode: true,
                network: 'devnet'
            };
            
            testStrategies.push(strategy);
            
            showResult('strategyResult', {
                success: true,
                strategy: strategy,
                message: '测试策略创建成功'
            }, '🎯 策略创建成功');
            
            successCount++;
            updateTestChecklist(2);
        }

        // 模拟策略运行
        function simulateStrategy() {
            testCount++;
            if (testStrategies.length === 0) {
                alert('请先创建策略');
                return;
            }
            
            const strategy = testStrategies[testStrategies.length - 1];
            strategy.status = 'running';
            strategy.profit = (Math.random() * 10 - 5).toFixed(2);
            
            showResult('strategyResult', {
                success: true,
                strategy: strategy,
                simulation: {
                    duration: '5分钟',
                    trades: Math.floor(Math.random() * 10) + 1,
                    profit: strategy.profit + '%'
                }
            }, '🧪 策略模拟完成');
            
            successCount++;
        }

        // 停止策略
        function stopStrategy() {
            if (testStrategies.length === 0) {
                alert('没有运行中的策略');
                return;
            }
            
            const strategy = testStrategies[testStrategies.length - 1];
            strategy.status = 'stopped';
            
            showResult('strategyResult', {
                success: true,
                strategy: strategy,
                message: '策略已停止'
            }, '⏹️ 策略已停止');
        }

        // 模拟交换
        function simulateSwap() {
            testCount++;
            const fromToken = document.getElementById('fromToken').value;
            const toToken = document.getElementById('toToken').value;
            const amount = parseFloat(document.getElementById('swapAmount').value);
            
            const rate = fromToken === 'SOL' ? 150 : 0.0067;
            const outputAmount = (amount * rate * (0.99 + Math.random() * 0.02)).toFixed(4);
            
            showResult('swapResult', {
                success: true,
                swap: {
                    from: fromToken,
                    to: toToken,
                    inputAmount: amount,
                    outputAmount: outputAmount,
                    rate: rate,
                    slippage: '0.1%'
                },
                txHash: 'swap_' + Date.now()
            }, '🔄 交换模拟成功');
            
            successCount++;
            updateTestChecklist(3);
        }

        // 获取报价
        function getQuote() {
            const fromToken = document.getElementById('fromToken').value;
            const toToken = document.getElementById('toToken').value;
            const amount = parseFloat(document.getElementById('swapAmount').value);
            
            showResult('swapResult', {
                success: true,
                quote: {
                    inputMint: fromToken,
                    outputMint: toToken,
                    inAmount: amount,
                    outAmount: (amount * 150).toFixed(4),
                    priceImpact: '0.1%'
                }
            }, '💰 报价获取成功');
        }

        // 运行全部测试
        function runAllTests() {
            generateTestWallet();
            setTimeout(() => airdropSOL(), 500);
            setTimeout(() => checkBalance(), 1000);
            setTimeout(() => createTestStrategy(), 1500);
            setTimeout(() => simulateStrategy(), 2000);
            setTimeout(() => simulateSwap(), 2500);
            
            setTimeout(() => {
                alert('🎉 全部测试完成！\n\n所有功能都运行正常，您可以安全地在测试网上进行实际测试。');
            }, 3000);
        }

        // 导出结果
        function exportResults() {
            const results = {
                testSummary: {
                    totalTests: testCount,
                    successfulTests: successCount,
                    successRate: Math.round((successCount / testCount) * 100) + '%',
                    duration: formatTime(Date.now() - startTime)
                },
                wallet: testWallet,
                strategies: testStrategies,
                timestamp: new Date().toISOString()
            };
            
            const blob = new Blob([JSON.stringify(results, null, 2)], {type: 'application/json'});
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'dlmm-test-results.json';
            a.click();
        }

        // 显示结果
        function showResult(elementId, data, message) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.innerHTML = `
                <div style="color: #28a745; font-weight: bold; margin-bottom: 10px;">${message}</div>
                <pre>${JSON.stringify(data, null, 2)}</pre>
            `;
        }

        // 更新测试清单
        function updateTestChecklist(index) {
            const items = document.querySelectorAll('#testChecklist li');
            if (items[index]) {
                items[index].innerHTML = items[index].innerHTML.replace('☐', '✅');
            }
        }

        // 启动实时更新
        setInterval(updateStatus, 1000);
        updateStatus();

        // 页面加载完成
        window.addEventListener('load', () => {
            console.log('🧪 DLMM离线测试页面已加载');
            alert('🎉 DLMM离线测试环境已就绪！\n\n这是一个完全独立的测试环境，您可以安全地测试所有功能。\n\n建议先点击"运行全部测试"来体验完整流程。');
        });
    </script>
</body>
</html>
