<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚀 DLMM策略创建器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .content {
            padding: 40px;
        }

        .step {
            margin-bottom: 40px;
            padding: 30px;
            border: 2px solid #e1e8ed;
            border-radius: 10px;
            background: #f8f9fa;
        }

        .step h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.5em;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #2c3e50;
        }

        .form-group input, .form-group select, .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e8ed;
            border-radius: 8px;
            font-size: 1em;
            transition: border-color 0.3s;
        }

        .form-group input:focus, .form-group select:focus, .form-group textarea:focus {
            outline: none;
            border-color: #3498db;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1.1em;
            font-weight: 600;
            transition: all 0.3s ease;
            margin: 10px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .btn.success {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
        }

        .btn.warning {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }

        .preview-box {
            background: #f8f9fa;
            border: 2px solid #e1e8ed;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-ready { background: #28a745; }
        .status-pending { background: #ffc107; }
        .status-error { background: #dc3545; }

        .info-box {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 15px;
            margin: 15px 0;
            border-radius: 4px;
        }

        .warning-box {
            background: #fff3e0;
            border-left: 4px solid #ff9800;
            padding: 15px;
            margin: 15px 0;
            border-radius: 4px;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e1e8ed;
            border-radius: 4px;
            overflow: hidden;
            margin: 20px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
            width: 0%;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 DLMM策略创建器</h1>
            <p>创建您的第一个自动化流动性管理策略</p>
        </div>

        <div class="content">
            <!-- 步骤1：基础配置 -->
            <div class="step">
                <h2>📋 步骤1：基础策略配置</h2>
                
                <div class="form-group">
                    <label for="strategyName">策略名称</label>
                    <input type="text" id="strategyName" placeholder="例如：SOL-USDC自动管理策略" value="我的第一个策略">
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="strategyType">策略类型</label>
                        <select id="strategyType">
                            <option value="simple_y">SimpleY策略（推荐新手）</option>
                            <option value="grid">网格策略</option>
                            <option value="custom">自定义策略</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="poolAddress">DLMM池地址</label>
                        <input type="text" id="poolAddress" placeholder="输入Meteora DLMM池地址" value="示例池地址">
                    </div>
                </div>

                <div class="info-box">
                    💡 <strong>提示：</strong> SimpleY策略适合新手，会自动管理流动性位置，优化收益并控制风险。
                </div>
            </div>

            <!-- 步骤2：资金配置 -->
            <div class="step">
                <h2>💰 步骤2：资金配置</h2>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="yAmount">Y代币数量</label>
                        <input type="number" id="yAmount" placeholder="100" value="100" min="1">
                    </div>
                    <div class="form-group">
                        <label for="xAmount">X代币数量（可选）</label>
                        <input type="number" id="xAmount" placeholder="自动计算" min="0">
                    </div>
                </div>

                <div class="warning-box">
                    ⚠️ <strong>风险提示：</strong> 建议新手先用小额资金测试，熟悉系统后再增加投入。
                </div>
            </div>

            <!-- 步骤3：策略参数 -->
            <div class="step">
                <h2>⚙️ 步骤3：策略参数</h2>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="binRange">价格区间范围</label>
                        <input type="number" id="binRange" value="30" min="5" max="100">
                        <small>建议：20-50，数值越大覆盖价格范围越广</small>
                    </div>
                    <div class="form-group">
                        <label for="slippageBps">滑点容忍度(基点)</label>
                        <input type="number" id="slippageBps" value="100" min="50" max="500">
                        <small>100基点 = 1%</small>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="upwardTimeout">向上超时(分钟)</label>
                        <input type="number" id="upwardTimeout" value="180" min="30" max="1440">
                    </div>
                    <div class="form-group">
                        <label for="downwardTimeout">向下超时(分钟)</label>
                        <input type="number" id="downwardTimeout" value="45" min="15" max="360">
                    </div>
                </div>
            </div>

            <!-- 步骤4：风险控制 -->
            <div class="step">
                <h2>🛡️ 步骤4：风险控制</h2>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="stopLossCount">止损次数</label>
                        <input type="number" id="stopLossCount" value="1" min="1" max="5">
                    </div>
                    <div class="form-group">
                        <label for="stopLossBinOffset">止损偏移</label>
                        <input type="number" id="stopLossBinOffset" value="5" min="1" max="20">
                    </div>
                </div>

                <div class="form-group">
                    <label for="maxLossPercentage">最大亏损百分比</label>
                    <input type="number" id="maxLossPercentage" value="5" min="1" max="20" step="0.1">
                    <small>达到此亏损比例将自动停止策略</small>
                </div>
            </div>

            <!-- 策略预览 -->
            <div class="step">
                <h2>👀 策略预览</h2>
                <div class="preview-box" id="strategyPreview">
                    点击"生成预览"查看策略配置
                </div>
                
                <div style="text-align: center; margin-top: 30px;">
                    <button class="btn" onclick="generatePreview()">🔍 生成预览</button>
                    <button class="btn success" onclick="createStrategy()">🚀 创建策略</button>
                    <button class="btn warning" onclick="testStrategy()">🧪 测试模式</button>
                </div>

                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                
                <div id="statusMessage" style="text-align: center; margin-top: 20px; font-weight: 600;">
                    <span class="status-indicator status-ready"></span>
                    准备创建策略
                </div>
            </div>
        </div>
    </div>

    <script>
        // 生成策略预览
        function generatePreview() {
            const strategy = collectFormData();
            const preview = document.getElementById('strategyPreview');
            
            preview.innerHTML = `
<strong>策略配置预览：</strong>

{
  "name": "${strategy.name}",
  "type": "${strategy.type}",
  "poolAddress": "${strategy.poolAddress}",
  "funds": {
    "yAmount": ${strategy.yAmount},
    "xAmount": ${strategy.xAmount || '自动计算'}
  },
  "parameters": {
    "binRange": ${strategy.binRange},
    "slippageBps": ${strategy.slippageBps},
    "upwardTimeout": ${strategy.upwardTimeout},
    "downwardTimeout": ${strategy.downwardTimeout}
  },
  "riskControl": {
    "stopLossCount": ${strategy.stopLossCount},
    "stopLossBinOffset": ${strategy.stopLossBinOffset},
    "maxLossPercentage": ${strategy.maxLossPercentage}%
  },
  "status": "待创建",
  "createdAt": "${new Date().toISOString()}"
}`;
            
            updateProgress(25);
            updateStatus('pending', '策略预览已生成');
        }

        // 收集表单数据
        function collectFormData() {
            return {
                name: document.getElementById('strategyName').value,
                type: document.getElementById('strategyType').value,
                poolAddress: document.getElementById('poolAddress').value,
                yAmount: parseFloat(document.getElementById('yAmount').value),
                xAmount: parseFloat(document.getElementById('xAmount').value) || null,
                binRange: parseInt(document.getElementById('binRange').value),
                slippageBps: parseInt(document.getElementById('slippageBps').value),
                upwardTimeout: parseInt(document.getElementById('upwardTimeout').value),
                downwardTimeout: parseInt(document.getElementById('downwardTimeout').value),
                stopLossCount: parseInt(document.getElementById('stopLossCount').value),
                stopLossBinOffset: parseInt(document.getElementById('stopLossBinOffset').value),
                maxLossPercentage: parseFloat(document.getElementById('maxLossPercentage').value)
            };
        }

        // 创建策略
        async function createStrategy() {
            const strategy = collectFormData();
            
            updateProgress(50);
            updateStatus('pending', '正在创建策略...');
            
            try {
                // 模拟API调用
                const response = await fetch('/api/strategy', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(strategy)
                });
                
                const result = await response.json();
                
                if (result.success) {
                    updateProgress(100);
                    updateStatus('ready', `策略创建成功！ID: ${result.data.id}`);
                    
                    // 显示成功信息
                    setTimeout(() => {
                        alert(`🎉 策略创建成功！\n\n策略ID: ${result.data.id}\n策略名称: ${result.data.name}\n\n您可以在监控页面查看策略运行状态。`);
                    }, 1000);
                } else {
                    throw new Error(result.error || '创建失败');
                }
                
            } catch (error) {
                updateProgress(0);
                updateStatus('error', `创建失败: ${error.message}`);
            }
        }

        // 测试模式
        function testStrategy() {
            const strategy = collectFormData();
            
            updateProgress(75);
            updateStatus('pending', '启动测试模式...');
            
            setTimeout(() => {
                updateProgress(100);
                updateStatus('ready', '测试模式已启动 - 使用模拟数据');
                
                alert(`🧪 测试模式已启动！\n\n策略将使用模拟数据运行，不会进行真实交易。\n您可以观察策略的行为和性能表现。\n\n测试完成后，可以切换到真实模式。`);
            }, 2000);
        }

        // 更新进度条
        function updateProgress(percentage) {
            document.getElementById('progressFill').style.width = percentage + '%';
        }

        // 更新状态
        function updateStatus(status, message) {
            const statusElement = document.getElementById('statusMessage');
            const indicator = statusElement.querySelector('.status-indicator');
            
            indicator.className = `status-indicator status-${status}`;
            statusElement.innerHTML = `<span class="status-indicator status-${status}"></span>${message}`;
        }

        // 页面加载完成
        window.addEventListener('load', () => {
            console.log('🚀 DLMM策略创建器已加载');
            updateProgress(0);
        });
    </script>
</body>
</html>
